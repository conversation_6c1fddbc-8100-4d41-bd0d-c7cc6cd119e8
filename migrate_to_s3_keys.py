#!/usr/bin/env python3
"""
Script para migrar URLs para S3 Keys

Este script executa a migração completa:
1. Aplica as migrations do Django
2. Executa o script SQL para migrar URLs para S3 keys
3. Atualiza os documentos do Elasticsearch

Uso:
    python migrate_to_s3_keys.py [--dry-run]
"""

import os
import sys
import subprocess
import argparse


def run_command(command, description):
    """Execute um comando e mostra o resultado"""
    print(f"\n{'='*60}")
    print(f"EXECUTANDO: {description}")
    print(f"COMANDO: {command}")
    print(f"{'='*60}")
    
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if result.stdout:
        print("STDOUT:")
        print(result.stdout)
    
    if result.stderr:
        print("STDERR:")
        print(result.stderr)
    
    if result.returncode != 0:
        print(f"ERRO: Comando falhou com código {result.returncode}")
        return False
    
    print("✅ Comando executado com sucesso")
    return True


def main():
    parser = argparse.ArgumentParser(description='Migrar URLs para S3 Keys')
    parser.add_argument('--dry-run', action='store_true', help='Executar em modo dry-run')
    args = parser.parse_args()
    
    print("🚀 INICIANDO MIGRAÇÃO PARA S3 KEYS")
    print(f"Modo: {'DRY-RUN' if args.dry_run else 'PRODUÇÃO'}")
    
    # Verificar se estamos no diretório correto
    if not os.path.exists('manage.py'):
        print("❌ ERRO: manage.py não encontrado. Execute este script no diretório raiz do projeto Django.")
        sys.exit(1)
    
    # 1. Aplicar migrations
    if not args.dry_run:
        if not run_command("python manage.py migrate", "Aplicando migrations do Django"):
            sys.exit(1)
    else:
        print("\n📋 DRY-RUN: Pulando aplicação de migrations")
    
    # 2. Executar script SQL
    print(f"\n📋 {'DRY-RUN: ' if args.dry_run else ''}Executando migração SQL...")
    if args.dry_run:
        print("Em modo dry-run, você deve executar manualmente:")
        print("psql -d sua_database -f migrate_urls_to_s3_keys.sql")
    else:
        print("Execute manualmente o script SQL:")
        print("psql -d sua_database -f migrate_urls_to_s3_keys.sql")
        input("Pressione Enter após executar o script SQL...")
    
    # 3. Atualizar Elasticsearch
    es_command = f"python manage.py migrate_elasticsearch_s3_keys"
    if args.dry_run:
        es_command += " --dry-run"
    
    if not run_command(es_command, "Atualizando documentos do Elasticsearch"):
        print("⚠️  AVISO: Falha ao atualizar Elasticsearch, mas a migração do banco foi concluída")
    
    print(f"\n🎉 MIGRAÇÃO {'DRY-RUN ' if args.dry_run else ''}CONCLUÍDA!")
    
    if not args.dry_run:
        print("\n📋 PRÓXIMOS PASSOS:")
        print("1. Verifique os resultados da migração no banco de dados")
        print("2. Teste algumas URLs para garantir que estão funcionando")
        print("3. Monitore os logs da aplicação")
        print("4. Considere remover as URLs antigas após confirmar que tudo está funcionando")


if __name__ == "__main__":
    main()
