# Migração para S3 Keys

## Visão Geral

Esta migração substitui o armazenamento de URLs completas por S3 Keys no banco de dados, proporcionando:

- **Portabilidade**: Facilita mudanças de bucket ou domínio CDN
- **Simplicidade**: S3 Keys são mais limpos e concisos
- **Flexibilidade**: URLs são construídas dinamicamente conforme necessário

## Mudanças Implementadas

### 1. Modelos Atualizados

**LearnContent**:
- Adicionado campo `s3_key` (CharField, 500 chars)
- Campo `url` agora é opcional (null=True, blank=True)
- Nova propriedade `full_url` que constrói URL dinamicamente

**MediaConversion**:
- Substituído `output_url` por `output_s3_key`
- Nova propriedade `output_url` que constrói URL dinamicamente

### 2. Serviços Atualizados

- **MediaConverterService**: Trabalha com S3 keys internamente
- **ContentService**: Constrói URLs dinamicamente para Elasticsearch
- **AWS S3 Service**: Retorna S3 key junto com URL

### 3. Elasticsearch

- Adicionado campo `s3_key` ao LearnContentDocument
- URLs construídas dinamicamente na formatação de documentos

## Como Executar a Migração

### Opção 1: Script Automatizado (Recomendado)

```bash
# Dry-run para testar
python migrate_to_s3_keys.py --dry-run

# Execução real
python migrate_to_s3_keys.py
```

### Opção 2: Execução Manual

1. **Aplicar migrations**:
```bash
python manage.py migrate
```

2. **Executar script SQL**:
```bash
psql -d sua_database -f migrate_urls_to_s3_keys.sql
```

3. **Atualizar Elasticsearch**:
```bash
# Dry-run primeiro
python manage.py migrate_elasticsearch_s3_keys --dry-run

# Execução real
python manage.py migrate_elasticsearch_s3_keys
```

## Verificação da Migração

### 1. Verificar Banco de Dados

```sql
-- Estatísticas da migração
SELECT 
    COUNT(*) as total_records,
    COUNT(url) as records_with_url,
    COUNT(s3_key) as records_with_s3_key,
    COUNT(CASE WHEN url IS NOT NULL AND s3_key IS NOT NULL THEN 1 END) as records_with_both
FROM learn_content;

-- Exemplos migrados
SELECT id, LEFT(url, 50) as url, s3_key 
FROM learn_content 
WHERE s3_key IS NOT NULL 
LIMIT 5;
```

### 2. Testar URLs

```python
# No Django shell
from learn_content.models import LearnContent

content = LearnContent.objects.filter(s3_key__isnull=False).first()
print(f"S3 Key: {content.s3_key}")
print(f"Full URL: {content.full_url}")
```

## Padrões de URL Suportados

O script SQL migra os seguintes padrões:

- `https://s3.amazonaws.com/bucket/key` → `key`
- `https://bucket.s3.region.amazonaws.com/key` → `key`
- `https://contents-stage.keepsdev.com/key` → `key`
- `https://contents.keepsdev.com/key` → `key`

## Rollback (Se Necessário)

Se precisar reverter:

1. **Restaurar URLs no banco**:
```sql
UPDATE learn_content 
SET url = CONCAT('https://contents-stage.keepsdev.com/', s3_key)
WHERE s3_key IS NOT NULL AND url IS NULL;
```

2. **Reverter migration**:
```bash
python manage.py migrate learn_content 0005
```

## Configurações Necessárias

Certifique-se de que estas configurações estão corretas no `settings.py`:

```python
# URL base para construção dinâmica de URLs
AWS_STREAMING_URL = "https://contents-stage.keepsdev.com"  # ou produção

# URL base do CDN para Media Converter
AWS_MEDIA_CONVERTER_CDN_BASE_URL = "https://contents-stage.keepsdev.com"
```

## Monitoramento Pós-Migração

1. **Logs da aplicação**: Monitore erros relacionados a URLs
2. **Elasticsearch**: Verifique se documentos estão sendo atualizados corretamente
3. **Media Converter**: Confirme que conversões continuam funcionando
4. **Frontend**: Teste reprodução de conteúdo

## Limpeza (Opcional)

Após confirmar que tudo está funcionando, você pode:

1. **Remover URLs antigas**:
```sql
UPDATE learn_content SET url = NULL WHERE s3_key IS NOT NULL;
```

2. **Remover campo URL** (em migration futura se desejado)

## Benefícios Alcançados

- ✅ **Portabilidade**: Mudanças de CDN não quebram registros
- ✅ **Simplicidade**: S3 keys são mais limpos
- ✅ **Flexibilidade**: URLs construídas conforme ambiente
- ✅ **Manutenibilidade**: Configuração centralizada
- ✅ **Compatibilidade**: Suporte a URLs legadas durante transição
