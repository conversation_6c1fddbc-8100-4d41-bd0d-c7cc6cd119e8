# Migração para S3 Keys

## Visão Geral

Esta migração otimiza o armazenamento de URLs, usando S3 Keys no banco de dados e URLs completas no Elasticsearch:

- **Banco de Dados**: Armazena S3 keys no campo `url` para portabilidade
- **Elasticsearch**: Armazena URLs completas para compatibilidade de API
- **APIs**: Mantém compatibilidade total retornando URLs completas

## Mudanças Implementadas

### 1. Modelos Atualizados

**LearnContent**:
- Campo `url` agora armazena S3 key (CharField, 500 chars)
- Propriedade `full_url` constrói URL completa dinamicamente
- Propriedade `s3_key` para compatibilidade (retorna o valor de `url`)

**MediaConversion**:
- Campo `output_url` agora armazena S3 key
- Propriedade `full_output_url` constrói URL completa dinamicamente
- Propriedade `output_s3_key` para compatibilidade

### 2. Serviços Atualizados

- **MediaConverterService**: Trabalha com S3 keys internamente
- **ContentService**: Elasticsearch mantém URLs completas (sem transformação)
- **AWS S3 Service**: Retorna S3 key no campo `url` e URL completa em `full_url`

### 3. Elasticsearch

- Armazena URLs completas para compatibilidade de API
- Celery task atualiza com URLs completas quando conversão completa

## Como Executar a Migração

### Opção 1: Script Automatizado (Recomendado)

```bash
# Dry-run para testar
python migrate_to_s3_keys.py --dry-run

# Execução real
python migrate_to_s3_keys.py
```

### Opção 2: Execução Manual

1. **Aplicar migrations**:
```bash
python manage.py migrate
```

2. **Executar script SQL**:
```bash
psql -d sua_database -f migrate_urls_to_s3_keys.sql
```

3. **Atualizar Elasticsearch**:
```bash
# Dry-run primeiro
python manage.py migrate_elasticsearch_s3_keys --dry-run

# Execução real
python manage.py migrate_elasticsearch_s3_keys
```

## Verificação da Migração

### 1. Verificar Banco de Dados

```sql
-- Estatísticas da migração
SELECT 
    COUNT(*) as total_records,
    COUNT(url) as records_with_url,
    COUNT(s3_key) as records_with_s3_key,
    COUNT(CASE WHEN url IS NOT NULL AND s3_key IS NOT NULL THEN 1 END) as records_with_both
FROM learn_content;

-- Exemplos migrados
SELECT id, LEFT(url, 50) as url, s3_key 
FROM learn_content 
WHERE s3_key IS NOT NULL 
LIMIT 5;
```

### 2. Testar URLs

```python
# No Django shell
from learn_content.models import LearnContent

content = LearnContent.objects.filter(url__isnull=False).first()
print(f"Stored in DB (S3 Key): {content.url}")
print(f"S3 Key property: {content.s3_key}")
print(f"Full URL property: {content.full_url}")
```

## Padrões de URL Suportados

O script SQL migra os seguintes padrões:

- `https://s3.amazonaws.com/bucket/key` → `key`
- `https://bucket.s3.region.amazonaws.com/key` → `key`
- `https://contents-stage.keepsdev.com/key` → `key`
- `https://contents.keepsdev.com/key` → `key`

## Rollback (Se Necessário)

Se precisar reverter:

1. **Restaurar URLs no banco** (se necessário):
```sql
-- Apenas se você tiver backup das URLs originais
-- Este exemplo reconstrói URLs do CDN
UPDATE learn_content
SET url = CONCAT('https://contents-stage.keepsdev.com/', url)
WHERE url NOT LIKE 'http%';
```

2. **Reverter migration**:
```bash
python manage.py migrate learn_content 0005
```

## Configurações Necessárias

Certifique-se de que estas configurações estão corretas no `settings.py`:

```python
# URL base para construção dinâmica de URLs
AWS_STREAMING_URL = "https://contents-stage.keepsdev.com"  # ou produção

# URL base do CDN para Media Converter
AWS_MEDIA_CONVERTER_CDN_BASE_URL = "https://contents-stage.keepsdev.com"
```

## Monitoramento Pós-Migração

1. **Logs da aplicação**: Monitore erros relacionados a URLs
2. **Elasticsearch**: Verifique se documentos estão sendo atualizados corretamente
3. **Media Converter**: Confirme que conversões continuam funcionando
4. **Frontend**: Teste reprodução de conteúdo

## Limpeza (Opcional)

Após confirmar que tudo está funcionando, você pode:

1. **Verificar consistência**:
```sql
-- Verificar se todas as URLs são S3 keys ou URLs externas válidas
SELECT COUNT(*) FROM learn_content
WHERE url LIKE 'http%' AND url LIKE '%amazonaws.com%';
-- Resultado deve ser 0 (todas URLs S3 foram convertidas para keys)
```

2. **Otimizações futuras**: Campo `url` já está otimizado para S3 keys

## Benefícios Alcançados

- ✅ **Portabilidade**: S3 keys no banco permitem mudanças de CDN sem quebrar registros
- ✅ **Compatibilidade**: APIs continuam retornando URLs completas
- ✅ **Performance**: Elasticsearch mantém URLs completas para respostas rápidas
- ✅ **Simplicidade**: S3 keys são limpos e concisos no banco
- ✅ **Flexibilidade**: URLs construídas dinamicamente conforme ambiente
- ✅ **Manutenibilidade**: Configuração centralizada em settings
