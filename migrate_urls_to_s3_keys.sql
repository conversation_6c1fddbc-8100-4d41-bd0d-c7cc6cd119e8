-- Script para migrar URLs existentes para S3 Keys
-- Execute este script após aplicar as migrations

-- 1. Atualizar LearnContent: substituir URLs completas por S3 keys
UPDATE learn_content
SET url = CASE
    -- URLs do tipo: https://s3.amazonaws.com/bucket/key
    WHEN url LIKE 'https://s3.amazonaws.com/%' THEN
        SUBSTRING(url FROM 'https://s3\.amazonaws\.com/[^/]+/(.+)')

    -- URLs do tipo: https://bucket.s3.region.amazonaws.com/key
    WHEN url LIKE 'https://%.s3.%.amazonaws.com/%' THEN
        SUBSTRING(url FROM 'https://[^/]+\.s3\.[^/]+\.amazonaws\.com/(.+)')

    -- URLs do tipo: https://contents-stage.keepsdev.com/key (CDN)
    WHEN url LIKE 'https://contents-stage.keepsdev.com/%' THEN
        SUBSTRING(url FROM 'https://contents-stage\.keepsdev\.com/(.+)')

    -- URLs do tipo: https://contents.keepsdev.com/key (CDN produção)
    WHEN url LIKE 'https://contents.keepsdev.com/%' THEN
        SUBSTRING(url FROM 'https://contents\.keepsdev\.com/(.+)')

    -- Outros padrões S3
    WHEN url LIKE '%amazonaws.com%' THEN
        REGEXP_REPLACE(url, '^https?://[^/]+/[^/]+/(.+)$', '\1')

    ELSE url  -- Manter URLs que não são S3 (YouTube, Vimeo, etc.)
END
WHERE url IS NOT NULL
  AND (
    url LIKE '%amazonaws.com%'
    OR url LIKE '%keepsdev.com%'
  );

-- 2. Verificar resultados da migração
SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN url LIKE 'http%' THEN 1 END) as records_with_full_urls,
    COUNT(CASE WHEN url NOT LIKE 'http%' AND url IS NOT NULL THEN 1 END) as records_with_s3_keys,
    COUNT(CASE WHEN url IS NULL THEN 1 END) as records_with_null_url
FROM learn_content;

-- 3. Mostrar alguns exemplos da migração (S3 keys)
SELECT
    id,
    LEFT(url, 80) as s3_key_or_url,
    CASE
        WHEN url LIKE 'http%' THEN 'FULL_URL'
        ELSE 'S3_KEY'
    END as url_type,
    created_date
FROM learn_content
WHERE url IS NOT NULL
ORDER BY created_date DESC
LIMIT 10;

-- 4. Mostrar URLs que não foram migradas (externos como YouTube, Vimeo, etc.)
SELECT
    id,
    LEFT(url, 80) as external_url,
    created_date
FROM learn_content
WHERE url LIKE 'http%'
  AND url NOT LIKE '%amazonaws.com%'
  AND url NOT LIKE '%keepsdev.com%'
ORDER BY created_date DESC
LIMIT 10;

-- 5. Atualizar Elasticsearch documents para incluir URLs completas
-- Nota: Este comando precisa ser executado via Django management command
-- python manage.py shell -c "
-- from learn_content.models import LearnContent
-- from learn_content.models.documents import LearnContentDocument
-- from config import settings
--
-- for content in LearnContent.objects.filter(url__isnull=False):
--     try:
--         doc = LearnContentDocument.get(content.id)
--         # Store full URL in Elasticsearch for API responses
--         if not content.url.startswith('http'):
--             doc.url = f'{settings.AWS_STREAMING_URL}/{content.url}'
--         else:
--             doc.url = content.url
--         doc.save()
--         print(f'Updated ES doc for content {content.id}')
--     except:
--         print(f'Could not update ES doc for content {content.id}')
-- "
