-- Script para migrar URLs existentes para S3 Keys
-- Execute este script após aplicar as migrations

-- 1. Atualizar LearnContent: extrair S3 key das URLs S3/CDN e manter URLs externas
UPDATE learn_content
SET s3_key = CASE
    -- URLs do tipo: https://s3.amazonaws.com/bucket/key
    WHEN url LIKE 'https://s3.amazonaws.com/%' THEN
        SUBSTRING(url FROM 'https://s3\.amazonaws\.com/[^/]+/(.+)')

    -- URLs do tipo: https://bucket.s3.region.amazonaws.com/key
    WHEN url LIKE 'https://%.s3.%.amazonaws.com/%' THEN
        SUBSTRING(url FROM 'https://[^/]+\.s3\.[^/]+\.amazonaws\.com/(.+)')

    -- URLs do tipo: https://contents-stage.keepsdev.com/key (CDN)
    WHEN url LIKE 'https://contents-stage.keepsdev.com/%' THEN
        SUBSTRING(url FROM 'https://contents-stage\.keepsdev\.com/(.+)')

    -- URLs do tipo: https://contents.keepsdev.com/key (CDN produção)
    WHEN url LIKE 'https://contents.keepsdev.com/%' THEN
        SUBSTRING(url FROM 'https://contents\.keepsdev\.com/(.+)')

    -- Outros padrões S3
    WHEN url LIKE '%amazonaws.com%' THEN
        REGEXP_REPLACE(url, '^https?://[^/]+/[^/]+/(.+)$', '\1')

    ELSE NULL  -- Não extrair S3 key de URLs externas (YouTube, Vimeo, etc.)
END
WHERE url IS NOT NULL
  AND s3_key IS NULL
  AND (
    url LIKE '%amazonaws.com%'
    OR url LIKE '%keepsdev.com%'
  );

-- 2. Verificar resultados da migração
SELECT
    COUNT(*) as total_records,
    COUNT(url) as records_with_url,
    COUNT(s3_key) as records_with_s3_key,
    COUNT(CASE WHEN url IS NOT NULL AND s3_key IS NOT NULL THEN 1 END) as records_with_both,
    COUNT(CASE WHEN url IS NULL AND s3_key IS NULL THEN 1 END) as records_with_neither
FROM learn_content;

-- 3. Mostrar alguns exemplos da migração
SELECT
    id,
    LEFT(url, 80) as original_url,
    LEFT(s3_key, 80) as extracted_s3_key,
    created_date
FROM learn_content
WHERE s3_key IS NOT NULL
ORDER BY created_date DESC
LIMIT 10;

-- 4. Mostrar URLs externas que mantiveram URL original (YouTube, Vimeo, etc.)
SELECT
    id,
    LEFT(url, 80) as external_url,
    created_date
FROM learn_content
WHERE url IS NOT NULL
  AND s3_key IS NULL
  AND url NOT LIKE 'http://localhost%'
ORDER BY created_date DESC
LIMIT 10;

-- 5. Atualizar Elasticsearch documents para incluir s3_key e URLs completas
-- Nota: Este comando precisa ser executado via Django management command
-- python manage.py migrate_elasticsearch_s3_keys
