#!/usr/bin/env python3

# Script para verificar se as tabelas do Django Admin foram removidas com sucesso
import os
import sys
import django

# Add the kontent directory to the path
sys.path.append('/home/<USER>/projects/keeps/keeps-kontent-server/kontent')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.db import connection
from django.conf import settings

def check_django_admin_tables():
    """Verifica se ainda existem tabelas do Django Admin no banco"""
    
    admin_tables = [
        'auth_user',
        'auth_group', 
        'auth_permission',
        'auth_user_groups',
        'auth_user_user_permissions',
        'auth_group_permissions',
        'django_content_type',
        'django_session',
        'django_admin_log'
    ]
    
    with connection.cursor() as cursor:
        # Busca todas as tabelas no banco
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        print("🔍 Verificando tabelas do Django Admin...")
        print("=" * 50)
        
        found_admin_tables = []
        for table in admin_tables:
            if table in existing_tables:
                found_admin_tables.append(table)
                print(f"❌ ENCONTRADA: {table}")
            else:
                print(f"✅ REMOVIDA: {table}")
        
        print("\n" + "=" * 50)
        
        if found_admin_tables:
            print(f"⚠️  ATENÇÃO: {len(found_admin_tables)} tabela(s) do Django Admin ainda existem:")
            for table in found_admin_tables:
                print(f"   - {table}")
            print("\nExecute o script SQL para removê-las.")
        else:
            print("🎉 SUCESSO: Todas as tabelas do Django Admin foram removidas!")
        
        print(f"\n📊 Total de tabelas no banco: {len(existing_tables)}")
        print("\n📋 Tabelas restantes:")
        for table in existing_tables:
            if not table.startswith('auth_') and table != 'django_content_type':
                print(f"   - {table}")

def check_installed_apps():
    """Verifica se os apps do Django Admin foram removidos do settings"""
    
    print("\n🔧 Verificando INSTALLED_APPS...")
    print("=" * 50)
    
    removed_apps = [
        'django.contrib.auth',
        'django.contrib.contenttypes'
    ]
    
    for app in removed_apps:
        if app in settings.INSTALLED_APPS:
            print(f"❌ AINDA PRESENTE: {app}")
        else:
            print(f"✅ REMOVIDO: {app}")
    
    print(f"\n📋 Apps instalados atualmente:")
    for app in settings.INSTALLED_APPS:
        print(f"   - {app}")

if __name__ == "__main__":
    print("🚀 Verificação de Remoção do Django Admin")
    print("=" * 60)
    
    try:
        check_django_admin_tables()
        check_installed_apps()
        
        print("\n" + "=" * 60)
        print("✅ Verificação concluída!")
        
    except Exception as e:
        print(f"❌ Erro durante a verificação: {e}")
        print("Certifique-se de que o banco de dados está acessível.")
