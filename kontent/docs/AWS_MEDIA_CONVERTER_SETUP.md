# AWS Media Converter Setup

Este documento explica como configurar e usar o AWS Media Converter para conversão automática de arquivos MP4 para formato DASH com streaming adaptativo.

## Configuração

### 1. Variáveis de Ambiente

Adicione as seguintes variáveis de ambiente:

```bash
# Obrigatório: ARN da role do IAM para o Media Converter
AWS_MEDIA_CONVERTER_ROLE_ARN=arn:aws:iam::ACCOUNT_ID:role/MediaConvertRole

# Opcional: Buckets específicos (padrão: usa AWS_BUCKET_NAME)
AWS_MEDIA_CONVERTER_INPUT_BUCKET=seu-bucket-input
AWS_MEDIA_CONVERTER_OUTPUT_BUCKET=seu-bucket-output

# Opcional: Queue específica (padrão: Default queue)
AWS_MEDIA_CONVERTER_QUEUE_ARN=arn:aws:mediaconvert:us-east-1:ACCOUNT_ID:queues/Default

# Opcional: Account ID (padrão: ************)
AWS_ACCOUNT_ID=************
```

### 2. IAM Role para Media Converter

Crie uma IAM Role com as seguintes permissões:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject"
            ],
            "Resource": [
                "arn:aws:s3:::SEU_BUCKET/*"
            ]
        }
    ]
}
```

Trust Policy:
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "mediaconvert.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
```

### 3. Permissões IAM para a Aplicação

Adicione as seguintes permissões ao usuário/role da aplicação:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "mediaconvert:CreateJob",
                "mediaconvert:GetJob",
                "mediaconvert:DescribeEndpoints"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": "iam:PassRole",
            "Resource": "arn:aws:iam::ACCOUNT_ID:role/MediaConvertRole"
        }
    ]
}
```

## Como Funciona

### 1. Upload de Arquivo MP4

Quando um arquivo MP4 é enviado via API:

1. O arquivo é processado normalmente (análise, transcription, etc.)
2. O sistema verifica se é um arquivo MP4
3. Se for MP4, inicia automaticamente um job DASH no AWS Media Converter
4. O `media_converter_job_id` é salvo no banco de dados
5. O `converted_url` é preenchido com a URL do manifest DASH esperado

### 2. Conversão DASH

O sistema converte MP4 para formato DASH com:
- **Múltiplas resoluções**: 1080p, 720p, 480p, 360p, 240p
- **Streaming adaptativo**: Qualidade ajusta automaticamente conforme a conexão
- **Thumbnails**: Gerados automaticamente a cada 5 segundos
- **Áudio separado**: Track de áudio AAC otimizado

### 3. Monitoramento de Jobs

Execute a task periódica para verificar o status dos jobs:

```python
# Task periódica (recomendado: a cada 5-10 minutos)
from tasks.media_converter.check_conversion_status import check_media_conversion_status
check_media_conversion_status.delay()

# Verificar job específico
from tasks.media_converter.check_conversion_status import check_single_conversion_status
check_single_conversion_status.delay(content_id)
```

### 4. Resultado da Conversão

Quando o job é concluído:
- O campo `converted_url` contém a URL do manifest DASH (.mpd)
- O documento no Elasticsearch é atualizado
- O arquivo original é preservado no campo `url`
- Thumbnails ficam disponíveis no diretório `/thumbnails/`

## Estrutura de Arquivos no S3

```
bucket/
├── clients/{workspace_id}/
│   ├── general/                    # Arquivos originais
│   │   └── {content_id}.mp4
│   └── mpdash/{content_id}/        # Arquivos DASH convertidos
│       ├── manifest.mpd            # Manifest principal
│       ├── {content_id}_1080p.mp4  # Vídeo 1080p
│       ├── {content_id}_720p.mp4   # Vídeo 720p
│       ├── {content_id}_480p.mp4   # Vídeo 480p
│       ├── {content_id}_360p.mp4   # Vídeo 360p
│       ├── {content_id}_240p.mp4   # Vídeo 240p
│       ├── {content_id}_audio.mp4  # Track de áudio
│       └── thumbnails/             # Thumbnails gerados
│           ├── {content_id}_thumb.00001.jpg
│           ├── {content_id}_thumb.00002.jpg
│           └── ...
```

## Configurações de Conversão DASH

O sistema está configurado para gerar:

### Vídeo (múltiplas resoluções):
- **1080p**: 1920x1080, máx 5 Mbps (QVBR)
- **720p**: 1280x720, máx 2.5 Mbps (QVBR)
- **480p**: 854x480, máx 1 Mbps (QVBR)
- **360p**: 640x360, máx 600 kbps (QVBR)
- **240p**: 426x240, máx 300 kbps (QVBR)

### Áudio:
- **Codec**: AAC
- **Bitrate**: 96 kbps
- **Sample Rate**: 48 kHz
- **Canais**: Stereo (2.0)

### DASH Settings:
- **Segment Length**: 6 segundos
- **Fragment Length**: 2 segundos
- **Profile**: Main Profile
- **Container**: MPD (MPEG-DASH)

### Thumbnails:
- **Resolução**: 640x360
- **Frequência**: A cada 5 segundos
- **Qualidade**: 80%
- **Formato**: JPEG

## Monitoramento e Logs

### Logs da Aplicação

```python
import logging
logger = logging.getLogger(__name__)

# Os logs incluem:
# - Início de conversão
# - Status de jobs
# - Erros de conversão
# - URLs de arquivos convertidos
```

### Verificar Status via API

```python
from learn_content.models import LearnContent

content = LearnContent.objects.get(id='content-id')
print(f"Job ID: {content.media_converter_job_id}")
print(f"Converted URL: {content.converted_url}")
```

## Troubleshooting

### Job não inicia
- Verifique se `AWS_MEDIA_CONVERTER_ROLE_ARN` está configurado
- Confirme que a role tem permissões corretas
- Verifique se o arquivo é realmente MP4

### Job falha
- Verifique os logs do Media Converter no AWS Console
- Confirme que o arquivo de entrada é válido
- Verifique permissões de S3

### URL convertida não é gerada
- Verifique se a task de monitoramento está rodando
- Confirme que o job foi concluído com sucesso
- Verifique se o arquivo foi criado no S3

## Exemplo de Uso

```python
from learn_content.services.media_converter_service import create_media_converter_service
from learn_content.models import LearnContent

# Obter serviço
service = create_media_converter_service()

# Verificar se arquivo deve ser convertido
content = LearnContent.objects.get(id='content-id')
should_convert = service.media_converter.should_convert_file(content.file_mime_type)

# Iniciar conversão DASH manualmente (se necessário)
if should_convert:
    workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"  # Exemplo
    service.convert_video_if_needed(content, workspace_id)

# Verificar status
status = service.update_conversion_status(content)
print(f"Status: {status}")

# URLs resultantes
print(f"Original: {content.url}")
print(f"DASH Manifest: {content.converted_url}")
# Exemplo: https://contents-stage.keepsdev.com/clients/workspace-id/mpdash/content-id/manifest.mpd
```

## URLs de Acesso

### Manifest DASH (URL principal para players):
```
https://contents-stage.keepsdev.com/clients/{workspace_id}/mpdash/{content_id}/manifest.mpd
```

### Arquivos individuais no S3:
```
https://s3.us-east-1.amazonaws.com/{bucket}/clients/{workspace_id}/mpdash/{content_id}/manifest.mpd
https://s3.us-east-1.amazonaws.com/{bucket}/clients/{workspace_id}/mpdash/{content_id}/{content_id}_720p.mp4
```

### Thumbnails:
```
https://s3.us-east-1.amazonaws.com/{bucket}/clients/{workspace_id}/mpdash/{content_id}/thumbnails/{content_id}_thumb.00001.jpg
```
