# AWS Media Converter Setup

## Overview

This document describes how to configure AWS Media Converter for automatic DASH video conversion in the Kontent system.

## Configuration

### Environment Variables

**All configuration is optional!** The system works out-of-the-box with sensible defaults.

Add these to your `.env` file only if you need custom values:

```bash
# OPTIONAL: Custom buckets (defaults to AWS_BUCKET_NAME)
# AWS_MEDIA_CONVERTER_INPUT_BUCKET=your-input-bucket
# AWS_MEDIA_CONVERTER_OUTPUT_BUCKET=your-output-bucket

# OPTIONAL: CDN base URL for converted content (defaults to stage)
# For production: AWS_MEDIA_CONVERTER_CDN_BASE_URL=https://contents.keepsdev.com
# For stage: AWS_MEDIA_CONVERTER_CDN_BASE_URL=https://contents-stage.keepsdev.com
# AWS_MEDIA_CONVERTER_CDN_BASE_URL=https://contents-stage.keepsdev.com

# OPTIONAL: Custom account ID (defaults to ************)
# AWS_ACCOUNT_ID=your-account-id
```

### Default Values

If not specified, the system uses these defaults:
- **Role**: `MediaConvert_Default_Role` (automatically builds ARN: `arn:aws:iam::************:role/service-role/MediaConvert_Default_Role`)
- **Queue**: `Default` (automatically builds ARN: `arn:aws:mediaconvert:us-east-1:************:queues/Default`)
- **CDN Base URL**: `https://contents-stage.keepsdev.com` (stage environment)
- **Account ID**: `************`
- **Buckets**: Uses `AWS_BUCKET_NAME` for both input and output

## AWS Setup

### 1. Create IAM Role

Create an IAM role named `MediaConvert_Default_Role` (or your custom name) with:

**Trust Policy:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "mediaconvert.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
```

**Permissions:**
- `AmazonS3FullAccess` (or custom S3 policy for your buckets)
- `AmazonAPIGatewayInvokeFullAccess` (if using API Gateway)

### 2. Media Converter Queue

The system uses the "Default" queue by default. You can create custom queues if needed.

## How It Works

### Automatic Conversion

1. When MP4 files are uploaded, the system automatically starts DASH conversion
2. Job ID is saved to `media_converter_job_id` field
3. Background task `CHECK_SINGLE_CONVERSION_STATUS` monitors job progress
4. When complete, `converted_url` is updated with DASH manifest URL
5. Elasticsearch document is updated with the new URL

### Output Format

- **Format**: DASH ISO with multiple resolutions
- **Resolutions**: 1080p, 720p, 480p, 360p, 240p
- **Audio**: AAC with separate audio tracks
- **Segmentation**: GOP-based for optimal streaming
- **Thumbnails**: Automatically generated

### URL Structure

```
Input:  https://bucket.s3.region.amazonaws.com/clients/workspace-id/general/content-id.mp4

Stage:  https://contents-stage.keepsdev.com/clients/workspace-id/mpdash/content-id/manifest.mpd
Prod:   https://contents.keepsdev.com/clients/workspace-id/mpdash/content-id/manifest.mpd
```

The CDN base URL is configurable via `AWS_MEDIA_CONVERTER_CDN_BASE_URL` environment variable.

## Monitoring

### Task Monitoring

The task `CHECK_SINGLE_CONVERSION_STATUS` is scheduled automatically when a job is initiated and reschedules itself until completion.

### Manual Job Check

```python
from tasks.media_converter.check_conversion_status import check_single_conversion_status
check_single_conversion_status.delay(content_id)
```

## Troubleshooting

### Common Issues

1. **Role not found**: Ensure the IAM role exists and has correct permissions
2. **S3 access denied**: Check bucket permissions and role policies
3. **Queue not found**: Verify queue exists or use "Default"

### Logs

Check application logs for Media Converter job status and errors:
```bash
# Check for conversion logs
grep "Media Converter" logs/application.log
grep "CHECK_SINGLE_CONVERSION_STATUS" logs/celery.log
```
