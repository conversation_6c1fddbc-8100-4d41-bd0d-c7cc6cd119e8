# AWS Media Converter Configuration
# Copy this file to your .env and configure the values

# REQUIRED: IAM Role ARN for Media Converter
# Create this role in AWS IAM with MediaConvert permissions
AWS_MEDIA_CONVERTER_ROLE_ARN=arn:aws:iam::************:role/service-role/MediaConvert_Default_Role

# OPTIONAL: Specific buckets for Media Converter (defaults to AWS_BUCKET_NAME)
# AWS_MEDIA_CONVERTER_INPUT_BUCKET=keeps.kontent.media.hml
# AWS_MEDIA_CONVERTER_OUTPUT_BUCKET=keeps.kontent.media.hml

# OPTIONAL: Specific queue for Media Converter (defaults to Default queue)
# AWS_MEDIA_CONVERTER_QUEUE_ARN=arn:aws:mediaconvert:us-east-1:************:queues/Default

# OPTIONAL: AWS Account ID (defaults to ************)
# AWS_ACCOUNT_ID=************

# Example URLs that will be generated:
# Input:  https://keeps.kontent.media.hml.s3.amazonaws.com/clients/workspace-id/general/content-id.mp4
# Output: https://contents-stage.keepsdev.com/clients/workspace-id/mpdash/content-id/manifest.mpd
