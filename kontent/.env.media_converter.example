# AWS Media Converter Configuration
# Copy these settings to your .env file

# ROLE CONFIGURATION (SIMPLIFIED)
# You can provide just the role name (recommended) or the full ARN
# Examples:
#   Just role name: AWS_MEDIA_CONVERTER_ROLE_ARN=MediaConvert_Default_Role
#   Full ARN: AWS_MEDIA_CONVERTER_ROLE_ARN=arn:aws:iam::************:role/service-role/MediaConvert_Default_Role
# If not provided, defaults to "MediaConvert_Default_Role"
AWS_MEDIA_CONVERTER_ROLE_ARN=MediaConvert_Default_Role

# OPTIONAL: Specific buckets for Media Converter (defaults to AWS_BUCKET_NAME)
# AWS_MEDIA_CONVERTER_INPUT_BUCKET=keeps.kontent.media.hml
# AWS_MEDIA_CONVERTER_OUTPUT_BUCKET=keeps.kontent.media.hml

# OPTIONAL: Specific queue for Media Converter (defaults to Default queue)
# You can provide just the queue name or the full ARN
# AWS_MEDIA_CONVERTER_QUEUE_ARN=Default

# OPTIONAL: AWS Account ID (defaults to ************)
# AWS_ACCOUNT_ID=************

# Example URLs that will be generated:
# Input:  https://keeps.kontent.media.hml.s3.amazonaws.com/clients/workspace-id/general/content-id.mp4
# Output: https://contents-stage.keepsdev.com/clients/workspace-id/mpdash/content-id/manifest.mpd
