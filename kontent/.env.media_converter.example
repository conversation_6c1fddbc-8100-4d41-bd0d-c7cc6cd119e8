# AWS Media Converter Configuration
# Copy these settings to your .env file (ALL OPTIONAL)

# ROLE CONFIGURATION (OPTIONAL)
# If not provided, defaults to "MediaConvert_Default_Role"
# You can provide just the role name or the full ARN:
#   Just role name: AWS_MEDIA_CONVERTER_ROLE_ARN=MyCustomRole
#   Full ARN: AWS_MEDIA_CONVERTER_ROLE_ARN=arn:aws:iam::************:role/service-role/MyCustomRole
# AWS_MEDIA_CONVERTER_ROLE_ARN=MediaConvert_Default_Role

# OPTIONAL: Specific buckets for Media Converter (defaults to AWS_BUCKET_NAME)
# AWS_MEDIA_CONVERTER_INPUT_BUCKET=keeps.kontent.media.hml
# AWS_MEDIA_CONVERTER_OUTPUT_BUCKET=keeps.kontent.media.hml

# OPTIONAL: Specific queue for Media Converter (defaults to Default queue)
# You can provide just the queue name or the full ARN
# AWS_MEDIA_CONVERTER_QUEUE_ARN=Default

# OPTIONAL: CDN base URL for converted content (defaults to stage)
# For production: AWS_MEDIA_CONVERTER_CDN_BASE_URL=https://contents.keepsdev.com
# For stage: AWS_MEDIA_CONVERTER_CDN_BASE_URL=https://contents-stage.keepsdev.com
# AWS_MEDIA_CONVERTER_CDN_BASE_URL=https://contents-stage.keepsdev.com

# OPTIONAL: AWS Account ID (defaults to ************)
# AWS_ACCOUNT_ID=************

# Example URLs that will be generated:
# Input:  https://keeps.kontent.media.hml.s3.amazonaws.com/clients/workspace-id/general/content-id.mp4
# Stage:  https://contents-stage.keepsdev.com/clients/workspace-id/mpdash/content-id/manifest.mpd
# Prod:   https://contents.keepsdev.com/clients/workspace-id/mpdash/content-id/manifest.mpd
