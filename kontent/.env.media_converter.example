# AWS Media Converter Configuration
# Copy these settings to your .env file (ALL OPTIONAL)

# OPTIONAL: Specific buckets for Media Converter (defaults to AWS_BUCKET_NAME)
# AWS_MEDIA_CONVERTER_INPUT_BUCKET=keeps.kontent.media.hml
# AWS_MEDIA_CONVERTER_OUTPUT_BUCKET=keeps.kontent.media.hml

# OPTIONAL: CDN base URL for converted content (defaults to stage)
# For production: AWS_MEDIA_CONVERTER_CDN_BASE_URL=https://contents.keepsdev.com
# For stage: AWS_MEDIA_CONVERTER_CDN_BASE_URL=https://contents-stage.keepsdev.com
# AWS_MEDIA_CONVERTER_CDN_BASE_URL=https://contents-stage.keepsdev.com

# OPTIONAL: AWS Account ID (defaults to ************)
# AWS_ACCOUNT_ID=************

# Example URLs that will be generated:
# Input:  https://keeps.kontent.media.hml.s3.amazonaws.com/clients/workspace-id/general/content-id.mp4
# Stage:  https://contents-stage.keepsdev.com/clients/workspace-id/mpdash/content-id/manifest.mpd
# Prod:   https://contents.keepsdev.com/clients/workspace-id/mpdash/content-id/manifest.mpd
