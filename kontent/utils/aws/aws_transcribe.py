from __future__ import print_function

import os
import json
import time
import uuid
import boto3

from custom.discord_webhook import DiscordWebhookLogger


# pylint: disable=invalid-name
# pylint: disable=too-many-instance-attributes
# pylint: disable=too-many-arguments
# pylint: disable=too-many-locals
class AmazonTranscribe:
    def __init__(self,
                 access_key,
                 secret_access,
                 region_name,
                 s3_client,
                 s3_bucket,
                 default_vocabulary,
                 webhook_logger: DiscordWebhookLogger,
                 cloudconvert_client):

        self._bucket = s3_bucket
        self._bucket_sub_folder = 'file_to_transcribe'
        self._bucket_sub_folder_converter = 'converters'
        self._convert_extension = 'flac'
        self._access_key = access_key
        self._secret_access = secret_access
        self._webhook_logger = webhook_logger
        self._transcribe = boto3.client('transcribe',
                                        aws_access_key_id=self._access_key,
                                        aws_secret_access_key=self._secret_access,
                                        region_name=region_name)

        self.s3 = s3_client
        self.default_vocabulary = default_vocabulary
        self.voc_language_map = {
            'keeps_pt_br': 'pt-BR',
            'keeps_en_us': 'en-US'
        }

        self._allowed_extensions = ["amr", "flac", "wav", "ogg", "mp3", "mp4", "webm"]
        self.cloudconvert_client = cloudconvert_client

    def transcribe(self, file_path, vocab_name=None):
        """
        @param file_path:
        @param vocab_name:
        @return:
        """

        extension = self._get_file_extension(file_path)
        job_name = str(uuid.uuid4())
        file_name = f'{job_name}.{extension}'

        s3_object = self.s3.send_file(file_name=file_name, file_path=file_path,
                                      bucket=self._bucket, sub_folder=self._bucket_sub_folder)

        if extension not in self._allowed_extensions:
            new_file = self._convert_file(file_name, extension)
            # Construct S3 URI from s3_key for converted file
            converted_key = s3_object['s3_key'].replace(self._bucket_sub_folder, self._bucket_sub_folder_converter).replace(file_name, new_file)
            job_uri = f"s3://{self._bucket}/{converted_key}"
            key_to_delete = f'{self._bucket_sub_folder_converter}/{new_file}'
            extension = self._convert_extension

        else:
            # Construct S3 URI from s3_key
            job_uri = f"s3://{self._bucket}/{s3_object['s3_key']}"
            key_to_delete = s3_object['name']

        try:
            self._transcribe.start_transcription_job(
                TranscriptionJobName=job_name,
                Media={'MediaFileUri': job_uri},
                MediaFormat=extension,
                IdentifyLanguage=True,
                OutputBucketName=self._bucket)

            while True:
                status = self._transcribe.get_transcription_job(TranscriptionJobName=job_name)
                if status['TranscriptionJob']['TranscriptionJobStatus'] in ['COMPLETED', 'FAILED']:
                    break
                print("Not ready yet...")  # noqa: T201
                time.sleep(5)

            if status['TranscriptionJob']['TranscriptionJobStatus'] == 'FAILED':
                raise Exception(status['TranscriptionJob']['FailureReason'])

            transcript_raw = self.get_transcribe_file(bucket_name=self._bucket, file_name=f'{job_name}.json')
            entire_transcript, sentences_and_times = self.get_transcript_text_and_timestamps(transcript_raw)

            self._transcribe.delete_transcription_job(TranscriptionJobName=job_name)
            self.s3.client.delete_object(Bucket=self._bucket, Key=key_to_delete)

            return entire_transcript[0]['transcript'], sentences_and_times

        except Exception as error:
            self._webhook_logger.emit_short_message('ELASTICSEARCH ERROR', error)
            raise error

    def transcribe_async(self, file_url, vocab_name=None) -> None:
        """
        @param file_url:
        @param vocab_name:
        @return job: dict with
        """
        job_name = str(uuid.uuid4())
        # Construct S3 URI from s3_key (file_url is now just the S3 key)
        job_uri = f"s3://{self._bucket}/{file_url}"
        print(job_uri)

        if not vocab_name:
            vocab_name = self.default_vocabulary

        job = self._transcribe.start_transcription_job(
            TranscriptionJobName=job_name,
            Media={'MediaFileUri': job_uri},
            LanguageCode=self.voc_language_map[vocab_name],
            Settings={
                'VocabularyName': vocab_name
            },
            OutputBucketName=self._bucket
        )

        return job

    def get_transcribe_job(self, job_name):
        return self._transcribe.get_transcription_job(TranscriptionJobName=job_name)

    def get_transcribe_file(self, bucket_name, file_name):
        s3_client_obj = self.s3.client.get_object(Bucket=bucket_name, Key=file_name)
        s3_client_data = s3_client_obj["Body"].read().decode("utf-8")
        return json.loads(s3_client_data)

    @staticmethod
    def get_transcript_text_and_timestamps(transcript_raw):
        items = transcript_raw["results"]["items"]
        entire_transcript = transcript_raw["results"]["transcripts"]

        sentences_and_times = []
        temp_sentence = ""
        temp_start_time = 0
        temp_min_confidence = 1.0
        new_sentence = True

        confidences = []
        scores = []

        i = 0
        for item in items:
            # always add the word
            if item["type"] == "punctuation":
                temp_sentence = (temp_sentence.strip() + item["alternatives"][0]["content"] + " ")
            else:
                temp_sentence = temp_sentence + item["alternatives"][0]["content"] + " "
                temp_min_confidence = min(temp_min_confidence, float(item["alternatives"][0]["confidence"]))
                confidences.append({"start_time": float(item["start_time"]),
                                    "end_time": float(item["end_time"]),
                                    "content": item["alternatives"][0]["content"],
                                    "confidence": float(item["alternatives"][0]["confidence"])
                                    })
                scores.append(float(item["alternatives"][0]["confidence"]))

            # if this is a new sentence, and it starts with a word, save the time
            if new_sentence:
                if item["type"] == "pronunciation":
                    temp_start_time = float(item["start_time"])
                new_sentence = False
            # else, keep going until you hit a punctuation
            else:
                if item["type"] == "punctuation" and item["alternatives"][0]["content"] != ",":
                    # end time of sentence is end_time of previous word
                    end_time = items[i - 1]["end_time"] if i - 1 >= 0 else items[0]["end_time"]
                    sentences_and_times.append(
                        {"start_time": temp_start_time,
                         "end_time": end_time,
                         "sentence": temp_sentence.strip(),
                         "min_confidence": temp_min_confidence
                         }
                    )
                    # reset the temp sentence and relevant variables
                    new_sentence = True
                    temp_sentence = ""
                    temp_min_confidence = 1.0

            i = i + 1

        sentences_and_times.append(
            {"start_time": temp_start_time,
             "end_time": confidences[-1]["end_time"] if len(confidences) > 0 else 0,
             "sentence": temp_sentence.strip(),
             "min_confidence": temp_min_confidence
             }
        )
        return entire_transcript, sentences_and_times

    # pylint: disable=consider-using-with
    def create_or_update_vocabulary(self, list_word_sentences, vocabulary):
        vocabulary_file = f'{vocabulary}.txt'
        s3_client_obj = self.s3.client.get_object(_bucket=self._bucket, Key=vocabulary_file)
        s3_client_data = s3_client_obj["Body"].read().decode("utf-8")

        f = open(vocabulary_file, "w")  # noqa: SIM115
        f.write(s3_client_data)

        for item in list_word_sentences:
            f.write("{}\t\t\t{}\n".format(item.replace(" ", "-").lower(), item))
        f.close()

        self.s3.client.upload_file(vocabulary_file, self._bucket, vocabulary_file)
        os.remove(vocabulary_file)

        vocabulary_published = self.get_vocabulary(vocabulary)

        if vocabulary_published:
            while True:
                if vocabulary_published['VocabularyState'] in ['READY']:
                    break
                print("Not ready yet...")  # noqa: T201
                time.sleep(5)
                vocabulary_published = self.get_vocabulary(vocabulary)

            return self._transcribe.update_vocabulary(
                VocabularyName=vocabulary,
                LanguageCode='pt-BR',
                VocabularyFileUri=f'https://s3.amazonaws.com/{self._bucket}/{vocabulary_file}'
            )
        return self._transcribe.create_vocabulary(
            VocabularyName=vocabulary,
            LanguageCode='pt-BR',
            VocabularyFileUri=f'https://s3.amazonaws.com/{self._bucket}/{vocabulary_file}'
        )

    def get_vocabulary(self, vocabulary):
        try:
            return self._transcribe.get_vocabulary(VocabularyName=vocabulary)
        except self._transcribe.exceptions.BadRequestException:
            return None

    @staticmethod
    def _get_file_extension(filename):
        filename_split = filename.split('.')
        return filename_split[-1] if len(filename.split('.')) >= 2 else None

    @staticmethod
    def _get_filename(file_path, path_join=None):
        paths = file_path.split('/')
        filename = paths[-1] if len(paths) >= 1 else file_path

        if path_join:
            return path_join + filename, filename

        return filename, filename

    def _convert_file(self, s3_key, extension):
        process = self.cloudconvert_client.createProcess({
            "inputformat": extension,
            "outputformat": self._convert_extension
        })

        process.start({
            "input": {
                "s3": {
                    "accesskeyid": self._access_key,
                    "secretaccesskey": self._secret_access,
                    "bucket": f'{self._bucket}/{self._bucket_sub_folder}'
                }
            },
            "file": s3_key,
            "outputformat": self._convert_extension,
            "output": {
                "s3": {
                    "accesskeyid": self._access_key,
                    "secretaccesskey": self._secret_access,
                    "bucket": f'{self._bucket}/converters'
                }
            },
            "converteroptions": {
                "audio_codec": "FLAC",
                "audio_bitrate": 128,
                "audio_channels": 1,
                "audio_frequency": 44100,
                "audio_normalize": None,
                "trim_to": None,
                "trim_from": None,
                "strip_metatags": False,
                "command": None
            }
        })

        process.refresh()
        process.wait()
        self.s3.client.delete_object(Bucket=self._bucket, Key=f'{self._bucket_sub_folder}/{s3_key}')
        return s3_key.replace(extension, self._convert_extension)
