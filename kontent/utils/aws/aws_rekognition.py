from urllib.parse import urlparse
import boto3


class AwsRekognitionClient:

    def __init__(self, aws_access_key, aws_secret_key, aws_region):
        self._client = boto3.client('rekognition',
                                    aws_access_key_id=aws_access_key,
                                    aws_secret_access_key=aws_secret_key,
                                    region_name=aws_region)

    def detect_image_text(self, image_url):
        bucket, name = self.parse_bucket_params(image_url)
        _ = self._client.detect_text(
            Image={'S3Object': {'Bucket': bucket, 'Name': name}},
            Filters={'WordFilter': {'MinConfidence': 80}}
        )
        return self.process_text(_)

    @staticmethod
    def process_text(rekognition_object):
        text_lines = []
        for text_detect in rekognition_object["TextDetections"]:
            if text_detect["Type"] == 'LINE':
                text_lines.append(text_detect["DetectedText"])

        return ' '.join(text_lines)

    @staticmethod
    def parse_bucket_params(s3_url):
        parsed = urlparse(s3_url, allow_fragments=False)
        bucket = parsed.netloc  # This is the bucket name in s3://bucket/path format
        name = parsed.path.lstrip('/')  # Remove leading slash from path
        return bucket, name
