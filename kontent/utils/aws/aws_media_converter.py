# -*- coding: utf-8 -*-
import boto3
import uuid
from typing import Dict, Optional


class AwsMediaConverter:
    """
    Service to handle AWS Media Converter operations for video conversion
    """

    def __init__(self, access_key: str, secret_key: str, region: str, 
                 input_bucket: str, output_bucket: str, role_arn: str):
        """
        Initialize AWS Media Converter client
        
        Args:
            access_key: AWS access key
            secret_key: AWS secret key
            region: AWS region
            input_bucket: S3 bucket for input files
            output_bucket: S3 bucket for output files
            role_arn: IAM role ARN for Media Converter
        """
        self.region = region
        self.input_bucket = input_bucket
        self.output_bucket = output_bucket
        self.role_arn = role_arn
        
        # Initialize Media Converter client
        self.media_converter = boto3.client(
            'mediaconvert',
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=region
        )
        
        # Get the endpoint URL for Media Converter
        self._endpoint_url = self._get_endpoint_url()
        
        # Reinitialize with endpoint URL
        self.media_converter = boto3.client(
            'mediaconvert',
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=region,
            endpoint_url=self._endpoint_url
        )

    def _get_endpoint_url(self) -> str:
        """Get the Media Converter endpoint URL for the region"""
        try:
            response = self.media_converter.describe_endpoints()
            return response['Endpoints'][0]['Url']
        except Exception as e:
            raise Exception(f"Failed to get Media Converter endpoint: {str(e)}")

    def convert_mp4_to_optimized(self, input_s3_key: str, content_id: str) -> Dict[str, str]:
        """
        Convert MP4 file to optimized format using AWS Media Converter
        
        Args:
            input_s3_key: S3 key of the input MP4 file
            content_id: LearnContent ID for naming
            
        Returns:
            Dict containing job_id and output_s3_key
        """
        job_name = f"convert-{content_id}-{str(uuid.uuid4())[:8]}"
        
        # Define input
        input_uri = f"s3://{self.input_bucket}/{input_s3_key}"
        
        # Define output
        output_key_prefix = f"converted/{content_id}/"
        output_uri = f"s3://{self.output_bucket}/{output_key_prefix}"
        
        # Job settings for MP4 optimization
        job_settings = {
            "Role": self.role_arn,
            "Settings": {
                "Inputs": [
                    {
                        "FileInput": input_uri,
                        "AudioSelectors": {
                            "Audio Selector 1": {
                                "DefaultSelection": "DEFAULT"
                            }
                        },
                        "VideoSelector": {}
                    }
                ],
                "OutputGroups": [
                    {
                        "Name": "File Group",
                        "OutputGroupSettings": {
                            "Type": "FILE_GROUP_SETTINGS",
                            "FileGroupSettings": {
                                "Destination": output_uri
                            }
                        },
                        "Outputs": [
                            {
                                "NameModifier": "_optimized",
                                "ContainerSettings": {
                                    "Container": "MP4",
                                    "Mp4Settings": {
                                        "CslgAtom": "INCLUDE",
                                        "FreeSpaceBox": "EXCLUDE",
                                        "MoovPlacement": "PROGRESSIVE_DOWNLOAD"
                                    }
                                },
                                "VideoDescription": {
                                    "CodecSettings": {
                                        "Codec": "H_264",
                                        "H264Settings": {
                                            "RateControlMode": "QVBR",
                                            "QvbrSettings": {
                                                "QvbrQualityLevel": 7
                                            },
                                            "MaxBitrate": 2000000,
                                            "SceneChangeDetect": "ENABLED"
                                        }
                                    },
                                    "Width": 1280,
                                    "Height": 720
                                },
                                "AudioDescriptions": [
                                    {
                                        "CodecSettings": {
                                            "Codec": "AAC",
                                            "AacSettings": {
                                                "Bitrate": 128000,
                                                "CodingMode": "CODING_MODE_2_0",
                                                "SampleRate": 44100
                                            }
                                        }
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        }
        
        try:
            response = self.media_converter.create_job(
                Role=self.role_arn,
                Settings=job_settings["Settings"],
                Queue="Default"  # You might want to make this configurable
            )
            
            job_id = response['Job']['Id']
            output_s3_key = f"{output_key_prefix}{job_name}_optimized.mp4"
            
            return {
                "job_id": job_id,
                "output_s3_key": output_s3_key,
                "output_url": f"s3://{self.output_bucket}/{output_s3_key}"
            }
            
        except Exception as e:
            raise Exception(f"Failed to create Media Converter job: {str(e)}")

    def get_job_status(self, job_id: str) -> Dict[str, str]:
        """
        Get the status of a Media Converter job
        
        Args:
            job_id: The Media Converter job ID
            
        Returns:
            Dict containing job status information
        """
        try:
            response = self.media_converter.get_job(Id=job_id)
            job = response['Job']
            
            return {
                "job_id": job_id,
                "status": job['Status'],
                "progress": job.get('JobPercentComplete', 0),
                "created_at": job.get('CreatedAt'),
                "finished_at": job.get('FinishedAt'),
                "error_message": job.get('ErrorMessage')
            }
            
        except Exception as e:
            raise Exception(f"Failed to get job status: {str(e)}")

    def should_convert_file(self, file_mime_type: str, file_size_mb: float = None) -> bool:
        """
        Determine if a file should be converted based on mime type and size
        
        Args:
            file_mime_type: MIME type of the file
            file_size_mb: File size in MB (optional)
            
        Returns:
            Boolean indicating if file should be converted
        """
        # Convert MP4 files
        convertible_types = ['video/mp4']
        
        if file_mime_type not in convertible_types:
            return False
            
        # Optional: Add size limits
        # if file_size_mb and file_size_mb > 500:  # Skip very large files
        #     return False
            
        return True
