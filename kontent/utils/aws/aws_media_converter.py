# -*- coding: utf-8 -*-
import boto3
from typing import Dict, Optional


class AwsMediaConverter:

    def __init__(self, access_key: str, secret_key: str, region: str,
                 input_bucket: str, output_bucket: str, account_id: str = None):
        self.region = region
        self.input_bucket = input_bucket
        self.output_bucket = output_bucket
        self.account_id = account_id or "************"

        self.queue_arn = f"arn:aws:mediaconvert:{region}:{self.account_id}:queues/Default"
        self.role_arn = f"arn:aws:iam::{self.account_id}:role/service-role/MediaConvert_Default_Role"

        self.media_converter = boto3.client(
            'mediaconvert',
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=region
        )

    def convert_mp4_to_dash(self, input_s3_key: str, content_id: str, workspace_id: str = None) -> Dict[str, str]:
        """
        Convert MP4 file to DASH format with multiple resolutions and thumbnails

        Args:
            input_s3_key: S3 key of the input MP4 file
            content_id: LearnContent ID for naming
            workspace_id: Workspace ID for organizing files

        Returns:
            Dict containing job_id, dash_manifest_url, and thumbnail_path
        """
        # Define input
        input_uri = f"s3://{self.input_bucket}/{input_s3_key}"

        # Define output paths based on workspace structure
        if workspace_id:
            dash_output_path = f"s3://{self.output_bucket}/clients/{workspace_id}/mpdash/{content_id}/"
            thumbnail_output_path = f"s3://{self.output_bucket}/clients/{workspace_id}/mpdash/{content_id}/thumbnails/"
        else:
            dash_output_path = f"s3://{self.output_bucket}/mpdash/{content_id}/"
            thumbnail_output_path = f"s3://{self.output_bucket}/mpdash/{content_id}/thumbnails/"

        # Job settings for DASH with multiple resolutions and thumbnails
        job_settings = {
            "TimecodeConfig": {
                "Source": "ZEROBASED"
            },
            "OutputGroups": [
                # DASH ISO Group - Multiple video resolutions + audio
                {
                    "Name": "DASH ISO",
                    "Outputs": [
                        # 1080p
                        {
                            "ContainerSettings": { "Container": "MPD" },
                            "VideoDescription": {
                                "Width": 1920,
                                "Height": 1080,
                                "CodecSettings": {
                                    "Codec": "H_264",
                                    "H264Settings": {
                                        "MaxBitrate": 5000000,
                                        "RateControlMode": "QVBR",
                                        "SceneChangeDetect": "TRANSITION_DETECTION"
                                    }
                                }
                            },
                            "NameModifier": "_1080p"
                        },
                        # 720p
                        {
                            "ContainerSettings": { "Container": "MPD" },
                            "VideoDescription": {
                                "Width": 1280,
                                "Height": 720,
                                "CodecSettings": {
                                    "Codec": "H_264",
                                    "H264Settings": {
                                        "MaxBitrate": 2500000,
                                        "RateControlMode": "QVBR",
                                        "SceneChangeDetect": "TRANSITION_DETECTION"
                                    }
                                }
                            },
                            "NameModifier": "_720p"
                        },
                        # 480p
                        {
                            "ContainerSettings": { "Container": "MPD" },
                            "VideoDescription": {
                                "Width": 854,
                                "Height": 480,
                                "CodecSettings": {
                                    "Codec": "H_264",
                                    "H264Settings": {
                                        "MaxBitrate": 1000000,
                                        "RateControlMode": "QVBR",
                                        "SceneChangeDetect": "TRANSITION_DETECTION"
                                    }
                                }
                            },
                            "NameModifier": "_480p"
                        },
                        # 360p
                        {
                            "ContainerSettings": { "Container": "MPD" },
                            "VideoDescription": {
                                "Width": 640,
                                "Height": 360,
                                "CodecSettings": {
                                    "Codec": "H_264",
                                    "H264Settings": {
                                        "MaxBitrate": 600000,
                                        "RateControlMode": "QVBR",
                                        "SceneChangeDetect": "TRANSITION_DETECTION"
                                    }
                                }
                            },
                            "NameModifier": "_360p"
                        },
                        # 240p
                        {
                            "ContainerSettings": { "Container": "MPD" },
                            "VideoDescription": {
                                "Width": 426,
                                "Height": 240,
                                "CodecSettings": {
                                    "Codec": "H_264",
                                    "H264Settings": {
                                        "MaxBitrate": 300000,
                                        "RateControlMode": "QVBR",
                                        "SceneChangeDetect": "TRANSITION_DETECTION"
                                    }
                                }
                            },
                            "NameModifier": "_240p"
                        },
                        # Audio track
                        {
                            "ContainerSettings": { "Container": "MPD" },
                            "AudioDescriptions": [
                                {
                                    "AudioSourceName": "Audio Selector 1",
                                    "CodecSettings": {
                                        "Codec": "AAC",
                                        "AacSettings": {
                                            "Bitrate": 96000,
                                            "CodingMode": "CODING_MODE_2_0",
                                            "SampleRate": 48000
                                        }
                                    }
                                }
                            ],
                            "NameModifier": "_audio"
                        }
                    ],
                    "OutputGroupSettings": {
                        "Type": "DASH_ISO_GROUP_SETTINGS",
                        "DashIsoGroupSettings": {
                            "SegmentLength": 6,
                            "Destination": dash_output_path,
                            "FragmentLength": 2,
                            "MpdProfile": "MAIN_PROFILE"
                        }
                    }
                },
                # Thumbnail Group
                {
                    "Name": "File Group",
                    "Outputs": [
                        {
                            "ContainerSettings": { "Container": "RAW" },
                            "VideoDescription": {
                                "Width": 640,
                                "Height": 360,
                                "CodecSettings": {
                                    "Codec": "FRAME_CAPTURE",
                                    "FrameCaptureSettings": {
                                        "FramerateNumerator": 1,
                                        "FramerateDenominator": 5,  # Thumbnail every 5 seconds
                                        "MaxCaptures": 1000,
                                        "Quality": 80
                                    }
                                }
                            },
                            "NameModifier": "_thumb"
                        }
                    ],
                    "OutputGroupSettings": {
                        "Type": "FILE_GROUP_SETTINGS",
                        "FileGroupSettings": {
                            "Destination": thumbnail_output_path
                        }
                    }
                }
            ],
            "Inputs": [
                {
                    "AudioSelectors": {
                        "Audio Selector 1": {
                            "DefaultSelection": "DEFAULT"
                        }
                    },
                    "VideoSelector": {},
                    "TimecodeSource": "ZEROBASED",
                    "FileInput": input_uri
                }
            ]
        }



        try:
            response = self.media_converter.create_job(
                Queue=self.queue_arn,
                Role=self.role_arn,
                Settings=job_settings,
                BillingTagsSource='JOB',
                AccelerationSettings={
                    'Mode': 'DISABLED'
                },
                StatusUpdateInterval='SECONDS_60',
                Priority=0,
                UserMetadata={
                    'video_id': str(content_id),
                    'workspace_id': str(workspace_id) if workspace_id else ''
                }
            )

            job_id = response['Job']['Id']

            # Build the manifest URL (this will be the main playback URL)
            from config import settings
            cdn_base_url = settings.AWS_STREAMING_URL

            if workspace_id:
                manifest_url = f"{cdn_base_url}/clients/{workspace_id}/mpdash/{content_id}/manifest.mpd"
            else:
                manifest_url = f"{cdn_base_url}/mpdash/{content_id}/manifest.mpd"

            return {
                "job_id": job_id,
                "dash_manifest_url": manifest_url,
                "thumbnail_path": thumbnail_output_path,
                "dash_output_path": dash_output_path
            }

        except Exception as e:
            raise Exception(f"Failed to create Media Converter DASH job: {str(e)}")

    def get_job_status(self, job_id: str) -> Dict[str, str]:
        """
        Get the status of a Media Converter job

        Args:
            job_id: The Media Converter job ID

        Returns:
            Dict containing job status information
        """
        try:
            response = self.media_converter.get_job(Id=job_id)
            job = response['Job']

            return {
                "job_id": job_id,
                "status": job['Status'],
                "progress": job.get('JobPercentComplete', 0),
                "created_at": job.get('CreatedAt'),
                "finished_at": job.get('FinishedAt'),
                "error_message": job.get('ErrorMessage')
            }

        except Exception as e:
            raise Exception(f"Failed to get job status: {str(e)}")

    def should_convert_file(self, file_mime_type: str, file_size_mb: float = None) -> bool:
        """
        Determine if a file should be converted based on mime type and size
        
        Args:
            file_mime_type: MIME type of the file
            file_size_mb: File size in MB (optional)
            
        Returns:
            Boolean indicating if file should be converted
        """
        # Convert MP4 files
        convertible_types = ['video/mp4']
        
        if file_mime_type not in convertible_types:
            return False
            
        # Optional: Add size limits
        # if file_size_mb and file_size_mb > 500:  # Skip very large files
        #     return False
            
        return True
