#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
<PERSON>ript to migrate existing media converter data to the new MediaConversion model.
This script can be run manually to test the migration before running the actual Django migration.
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from learn_content.models import LearnContent
from learn_content.models.media_conversion import MediaConversion


def migrate_data():
    """
    Migrate existing media_converter_job_id and converted_url data 
    from LearnContent to MediaConversion model
    """
    print("Starting media conversion data migration...")
    
    # Find all content with media converter data
    contents_with_jobs = LearnContent.objects.filter(
        media_converter_job_id__isnull=False
    ).exclude(media_converter_job_id='')
    
    print(f"Found {contents_with_jobs.count()} content items with media converter data")
    
    migrated_count = 0
    
    for content in contents_with_jobs:
        # Check if conversion already exists
        existing_conversion = MediaConversion.objects.filter(
            content=content,
            aws_job_id=content.media_converter_job_id
        ).first()
        
        if existing_conversion:
            print(f"Skipping content {content.id} - conversion already exists")
            continue
        
        # Determine status based on converted_url
        if content.converted_url:
            status = "COMPLETE"
            output_url = content.converted_url
            progress = 100
        else:
            # Job exists but no URL - likely still processing or failed
            status = "PROGRESSING"  # We'll let the task update this
            output_url = None
            progress = 0
        
        # Extract workspace_id from URL if possible
        workspace_id = None
        if content.url:
            try:
                url_parts = content.url.split('/')
                if 'clients' in url_parts:
                    client_index = url_parts.index('clients')
                    if client_index + 1 < len(url_parts):
                        workspace_id = url_parts[client_index + 1]
            except Exception as e:
                print(f"Could not extract workspace_id from URL {content.url}: {e}")
        
        # Create MediaConversion record
        conversion = MediaConversion.objects.create(
            content=content,
            conversion_type="MP4_TO_DASH",
            aws_job_id=content.media_converter_job_id,
            status=status,
            progress=progress,
            input_url=content.url,
            output_url=output_url,
            workspace_id=workspace_id,
            # We don't have historical timing data, so leave these null
            job_started_at=None,
            job_finished_at=None,
            error_message=None,
            error_code=None,
            retry_count=0,
            max_retries=3
        )
        
        print(f"Migrated content {content.id} -> conversion {conversion.id} (status: {status})")
        migrated_count += 1
    
    print(f"Successfully migrated {migrated_count} records to MediaConversion model")


def verify_migration():
    """
    Verify that the migration was successful
    """
    print("\nVerifying migration...")
    
    # Count original records
    original_count = LearnContent.objects.filter(
        media_converter_job_id__isnull=False
    ).exclude(media_converter_job_id='').count()
    
    # Count migrated records
    migrated_count = MediaConversion.objects.count()
    
    print(f"Original records with media converter data: {original_count}")
    print(f"Migrated MediaConversion records: {migrated_count}")
    
    # Check for any missing migrations
    missing_migrations = []
    for content in LearnContent.objects.filter(
        media_converter_job_id__isnull=False
    ).exclude(media_converter_job_id=''):
        if not MediaConversion.objects.filter(
            content=content,
            aws_job_id=content.media_converter_job_id
        ).exists():
            missing_migrations.append(content.id)
    
    if missing_migrations:
        print(f"WARNING: {len(missing_migrations)} content items were not migrated:")
        for content_id in missing_migrations[:10]:  # Show first 10
            print(f"  - Content ID: {content_id}")
        if len(missing_migrations) > 10:
            print(f"  ... and {len(missing_migrations) - 10} more")
    else:
        print("✓ All content items with media converter data have been migrated")


if __name__ == "__main__":
    try:
        migrate_data()
        verify_migration()
        print("\nMigration completed successfully!")
    except Exception as e:
        print(f"Migration failed: {e}")
        sys.exit(1)
