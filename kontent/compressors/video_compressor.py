import subprocess
import os
import re
from dataclasses import dataclass
from typing import Op<PERSON>, Tuple

from config.settings import VIDEO_COMPRESSOR_SETTINGS
from utils.aws import AmazonS3
from utils.extract_file_info import get_file_size


@dataclass
class VideoCompressorInput:
    file_path: str
    actual_file_url: str
    file_mime_type: str
    duration: Optional[float] = 0


class VideoCompressor:
    """
    Service to compress VIDEO File using preset inject in constructor
    """

    TYPES_TO_COMPRESS = [
        'video/mp4',
        'video/3gpp',
        'video/x-msvideo',
        'video/quicktime',
        'video/webm',
    ]

    def __init__(self,  uploader: AmazonS3, settings: dict = VIDEO_COMPRESSOR_SETTINGS):
        self.MAX_WIDTH = settings["MAX_WIDTH"]
        self.MAX_HEIGHT = settings["MAX_HEIGHT"]
        self.VIDEO_BITRATE = settings["VIDEO_BITRATE"]
        self.AUDIO_BITRATE = settings["AUDIO_BITRATE"]
        self.CRF_VALUE = settings["CRF"]
        self.PRESET = settings["PRESET"]
        self.MINIMUM_FILE_SIZE = settings["MINIMUM_FILE_SIZE"]
        self.MAXIMUM_DURATION = settings["MAXIMUM_DURATION"]
        self.uploader = uploader

    @staticmethod
    def calculate_new_scale(current_width, current_height, max_width=None, max_height=None):
        if not max_width and not max_height:
            return current_width, current_height

        aspect_ratio = current_width / current_height

        if max_width and (not max_height or current_width > max_width):
            new_width = max_width
            new_height = int(new_width / aspect_ratio)
        elif max_height and (not max_width or current_height > max_height):
            new_height = max_height
            new_width = int(new_height * aspect_ratio)
        else:
            return current_width, current_height

        return new_width, new_height

    @staticmethod
    def extract_video_dimensions(input_file: str):
        command = ['ffmpeg', '-i', input_file]
        result = subprocess.run(command, capture_output=True, text=True)

        dimension_pattern = r'\b(\d{2,})x(\d{2,})\b'
        for line in result.stderr.splitlines():
            if 'Video:' in line:
                match = re.search(dimension_pattern, line)
                if match:
                    width = int(match.group(1))
                    height = int(match.group(2))
                    return width, height
        raise ValueError(f"Could not extract dimensions from video: {input_file}. FFmpeg output: {result.stderr}")

    def build_ffmpeg_command(self, input_file, output_file, new_width, new_height):
        command = [
            'ffmpeg', '-y', '-i', input_file,
            '-c:v', 'libx264',
            '-b:v', self.VIDEO_BITRATE,
            '-c:a', 'aac',
            '-b:a', self.AUDIO_BITRATE,
            '-crf', str(self.CRF_VALUE),
            '-preset', self.PRESET,
            '-movflags', '+faststart',
            output_file
        ]

        if new_width and new_height:
            command.insert(16, '-vf')
            command.insert(17, f'scale={new_width}:{new_height}')

        return command

    def compress_file(self, input_file: str, output_file: str = None) -> str:
        current_width, current_height = self.extract_video_dimensions(input_file)
        new_width, new_height = self.calculate_new_scale(current_width, current_height, self.MAX_WIDTH, self.MAX_HEIGHT)

        if not output_file:
            base, ext = os.path.splitext(input_file)
            output_file = f"{base}_compressed{ext}"

        command = self.build_ffmpeg_command(input_file, output_file, new_width, new_height)
        subprocess.run(command, check=True)

        return output_file

    def compress_content(self, video_input: VideoCompressorInput) -> Tuple[str, float]:
        actual_size = get_file_size(video_input.file_path)
        if video_input.file_mime_type not in self.TYPES_TO_COMPRESS:
            return video_input.actual_file_url, actual_size
        if video_input.duration > self.MAXIMUM_DURATION:
            return video_input.actual_file_url, actual_size

        compressed_file = self.compress_file(video_input.file_path)
        os.remove(video_input.file_path)

        bucket, key = self.uploader.extract_s3_url_bucket_key(video_input.actual_file_url)
        if "/" in key:
            directory, filename = key.rsplit("/", 1)
            key = f"{directory}/compressed/{filename}"
        else:
            key = f"compressed/{key}"

        upload_response = self.uploader.send_file(
            file_name=key,
            file_path=compressed_file,
            bucket=bucket
        )

        return upload_response["s3_key"], get_file_size(compressed_file)
