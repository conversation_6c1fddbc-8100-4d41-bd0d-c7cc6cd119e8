from analyze import AnalyzeImage
from learn_content.models import LearnContent, ContentPointRule
from tasks.content_analyzers.update_doc import update_elastic_doc


def analyze_image_file(content: LearnContent):
    points_rules = ContentPointRule.objects.filter(content_type=content.content_type).first()
    points_quantity = points_rules.quantity
    analyzer = AnalyzeImage(
            file_name="",
            points_rules=points_rules.points,
            points_quantity=points_quantity,
            file_path=""
    )
    
    try:
        # Use full_url property which handles both s3_key and url cases
        image_url = content.full_url
        result = analyzer.process(image_url)
        if result.get('analyzed') is True:
            content.analyzed = True
            content.transcribe_job = None
            content.save()
            update_elastic_doc(content.id, result)
    except Exception as error:
        analyzer.logger('transcribe_process', f'error: {str(error)}, content: {content.id}')
        content.analyzed = True
        content.save()
        update_elastic_doc(content.id, {"duration": 0, "points": 0, "analyzed": True})
    