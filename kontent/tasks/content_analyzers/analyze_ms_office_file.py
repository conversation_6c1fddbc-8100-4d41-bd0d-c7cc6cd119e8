import os

from analyze import AnalyzeMsOffice
from di import Container
from learn_content.models import Learn<PERSON>ontent, ContentType
from tasks.content_analyzers.update_doc import update_elastic_doc


def analyze_ms_office_file(content: LearnContent):
    di = Container()
    s3_client = di.aws_s3_client()

    # Use s3_key if available, otherwise extract from URL
    if content.s3_key:
        key = content.s3_key
        bucket = s3_client.bucket_name
    else:
        bucket, key = s3_client.extract_s3_url_bucket_key(content.url)

    extension = key.split('.')[-1:][0]
    content_type = ContentType.objects.filter(extensions__icontains=extension).first()
    points_rules = content_type.contentpointrule_set.first()

    analyzer = AnalyzeMsOffice()
    analyzer._file_to_analyze = s3_client.download(key, bucket)
    analyzer._points_rules = points_rules.points
    analyzer._points_quantity = points_rules.quantity
    
    try:
        result = analyzer.process(extension)
        if result.get('analyzed') is True:
            content.analyzed = True
            content.transcribe_job = None
            content.save()
            update_elastic_doc(content.id, result)
    except Exception as error:
        analyzer.logger('transcribe_process', f'error: {str(error)}, content: {content.id}')
        content.analyzed = True
        content.save()
        update_elastic_doc(content.id, {"duration": 0, "points": 0, "analyzed": True})
