# This will make sure the app is always imported when
# Django starts so that shared_task will use this app.
import os
import django
from config.celery import app as celery_app

__all__ = ('celery_app',)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', "config.settings")
django.setup()

import tasks.analyzer
from tasks.content_analyzers.analyze_content import analyze_content
import tasks.media_converter
