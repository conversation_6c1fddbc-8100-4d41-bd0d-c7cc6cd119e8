# -*- coding: utf-8 -*-
import logging
from base_task import BaseTask
from config.celery import app
from learn_content.models import LearnContent
from learn_content.services.media_converter_service import create_media_converter_service
from tasks.content_analyzers.update_doc import update_elastic_doc
from task_names import CHECK_SINGLE_CONVERSION_STATUS

logger = logging.getLogger(__name__)



@app.task(name="check_single_conversion_status", base=BaseTask, track_started=True)
def check_single_conversion_status(content_id: str):
    """
    Check the conversion status for a specific content
    
    Args:
        content_id: UUID of the LearnContent
    """
    try:
        content = LearnContent.objects.get(id=content_id)
        
        if not content.media_converter_job_id:
            logger.warning(f"Content {content_id} has no media converter job ID")
            return
            
        if content.converted_url:
            logger.info(f"Content {content_id} already has converted URL")
            return

        media_converter_service = create_media_converter_service()
        status = media_converter_service.update_conversion_status(content)
        
        logger.info(f"Conversion status for content {content_id}: {status}")
        
        if status == "COMPLETE" and content.converted_url:
            # Update Elasticsearch document
            update_elastic_doc(content.id, {"converted_url": content.converted_url})
        elif status == "PROGRESSING":
            # Schedule another check in 60 seconds
            app.send_task(
                CHECK_SINGLE_CONVERSION_STATUS,
                args=(content_id,),
                countdown=60
            )

    except LearnContent.DoesNotExist:
        logger.error(f"Content {content_id} not found")
    except Exception as e:
        logger.error(f"Failed to check conversion status for content {content_id}: {str(e)}")
