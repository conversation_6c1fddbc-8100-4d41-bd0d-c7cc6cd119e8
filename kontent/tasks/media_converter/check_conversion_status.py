# -*- coding: utf-8 -*-
import logging
from base_task import BaseTask
from config.celery import app
from learn_content.models import LearnContent
from learn_content.services.media_converter_service import create_media_converter_service
from tasks.content_analyzers.update_doc import update_elastic_doc
from task_names import CHECK_SINGLE_CONVERSION_STATUS

logger = logging.getLogger(__name__)


@app.task(name="CHECK_SINGLE_CONVERSION_STATUS", base=BaseTask, track_started=True)
def check_single_conversion_status(content_id: str):
    logger.info(f"Starting conversion status check for content {content_id}")

    try:
        content = LearnContent.objects.get(id=content_id)
        logger.info(f"Found content {content_id}, job_id: {content.media_converter_job_id}, converted_url: {content.converted_url}")

        if not content.media_converter_job_id:
            logger.warning(f"Content {content_id} has no media converter job ID")
            return

        if content.converted_url:
            logger.info(f"Content {content_id} already has converted URL: {content.converted_url}")
            return

        media_converter_service = create_media_converter_service()
        logger.info(f"Created media converter service for content {content_id}")

        status = media_converter_service.update_conversion_status(content)
        logger.info(f"Conversion status for content {content_id}: {status}")

        # Refresh content from database to get updated converted_url
        content.refresh_from_db()
        logger.info(f"After status update - content {content_id} converted_url: {content.converted_url}")

        if status == "COMPLETE" and content.converted_url:
            logger.info(f"Updating Elasticsearch for content {content_id} with URL: {content.converted_url}")
            update_elastic_doc(content.id, {"url": content.converted_url})
        elif status == "PROGRESSING":
            logger.info(f"Job still progressing for content {content_id}, scheduling next check in 60 seconds")
            app.send_task(
                CHECK_SINGLE_CONVERSION_STATUS,
                args=(content_id,),
                countdown=60
            )

    except LearnContent.DoesNotExist:
        logger.error(f"Content {content_id} not found")
    except Exception as e:
        logger.error(f"Failed to check conversion status for content {content_id}: {str(e)}")
