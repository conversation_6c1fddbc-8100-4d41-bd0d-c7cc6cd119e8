# -*- coding: utf-8 -*-
import logging
from base_task import BaseTask
from config.celery import app
from learn_content.models import LearnContent
from learn_content.models.media_conversion import MediaConversion
from learn_content.services.media_converter_service import create_media_converter_service
from tasks.content_analyzers.update_doc import update_elastic_doc
from task_names import CHECK_SINGLE_CONVERSION_STATUS

logger = logging.getLogger(__name__)


@app.task(name="CHECK_SINGLE_CONVERSION_STATUS", base=BaseTask, track_started=True)
def check_single_conversion_status(content_id: str):
    try:
        content = LearnContent.objects.get(id=content_id)

        conversion = content.get_media_conversion()

        if not conversion or not conversion.aws_job_id:
            logger.warning(f"Content {content_id} has no media conversion")
            return

        if conversion.is_complete:
            logger.info(f"Content {content_id} conversion already completed")
            return

        media_converter_service = create_media_converter_service()
        status = media_converter_service.update_conversion_status(content)

        logger.info(f"Conversion status for content {content_id}: {status}")

        if status == "COMPLETE" and conversion.output_url:
            update_elastic_doc(content.id, {"url": conversion.output_url})
        elif status == "SUBMITTED":
            app.send_task(
                CHECK_SINGLE_CONVERSION_STATUS,
                args=(content_id,),
                countdown=60
            )
        elif status in ["ERROR", "CANCELED"]:
            logger.error(f"Job failed for content {content_id} with status: {status}")
        else:
            logger.warning(f"Unknown job status for content {content_id}: {status}")

    except LearnContent.DoesNotExist:
        logger.error(f"Content {content_id} not found")
    except Exception as e:
        logger.error(f"Failed to check conversion status for content {content_id}: {str(e)}")
