#!/usr/bin/env python
"""
Script de teste para o AWS Media Converter DASH
Execute este script para testar a configuração do Media Converter
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from learn_content.services.media_converter_service import create_media_converter_service
from learn_content.models import LearnContent
from utils.aws.aws_media_converter import AwsMediaConverter
from django.conf import settings


def test_media_converter_config():
    """Testa se as configurações do Media Converter estão corretas"""
    print("🔧 Testando configurações do AWS Media Converter...")
    
    required_settings = [
        'AWS_ACCESS_KEY_ID',
        'AWS_SECRET_ACCESS_KEY', 
        'AWS_REGION_NAME',
        'AWS_MEDIA_CONVERTER_ROLE_ARN'
    ]
    
    missing_settings = []
    for setting in required_settings:
        if not getattr(settings, setting, None):
            missing_settings.append(setting)
    
    if missing_settings:
        print(f"❌ Configurações faltando: {', '.join(missing_settings)}")
        return False
    
    print("✅ Todas as configurações obrigatórias estão presentes")
    return True


def test_media_converter_client():
    """Testa se o cliente do Media Converter pode ser inicializado"""
    print("\n🔧 Testando inicialização do cliente Media Converter...")
    
    try:
        service = create_media_converter_service()
        print("✅ Serviço Media Converter criado com sucesso")
        
        # Teste básico de conectividade
        client = service.media_converter.media_converter
        endpoints = client.describe_endpoints()
        print(f"✅ Conectado ao endpoint: {endpoints['Endpoints'][0]['Url']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar serviço: {str(e)}")
        return False


def test_file_conversion_logic():
    """Testa a lógica de conversão de arquivos"""
    print("\n🔧 Testando lógica de conversão...")
    
    try:
        service = create_media_converter_service()
        
        # Teste com arquivo MP4
        should_convert_mp4 = service.media_converter.should_convert_file('video/mp4')
        print(f"✅ MP4 deve ser convertido: {should_convert_mp4}")
        
        # Teste com arquivo não-MP4
        should_convert_other = service.media_converter.should_convert_file('video/avi')
        print(f"✅ AVI deve ser convertido: {should_convert_other}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na lógica de conversão: {str(e)}")
        return False


def test_s3_key_extraction():
    """Testa extração de chaves S3 e workspace ID"""
    print("\n🔧 Testando extração de chaves S3...")
    
    try:
        service = create_media_converter_service()
        
        # URL de exemplo
        test_url = "https://keeps.kontent.media.hml.s3.amazonaws.com/clients/e76b5082-f4fe-4f41-be79-1977840e16a8/general/7966d050-6f10-4927-bb26-50d5922dfb2d.mp4"
        
        s3_key = service._extract_s3_key_from_url(test_url)
        print(f"✅ S3 Key extraída: {s3_key}")
        
        if s3_key:
            workspace_id = service._extract_workspace_id_from_s3_key(s3_key)
            print(f"✅ Workspace ID extraído: {workspace_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na extração de S3 key: {str(e)}")
        return False


def show_example_conversion():
    """Mostra exemplo de como seria uma conversão"""
    print("\n📋 Exemplo de conversão DASH:")
    print("Input: s3://bucket/clients/workspace-id/general/video.mp4")
    print("Output DASH: s3://bucket/clients/workspace-id/mpdash/content-id/")
    print("  ├── manifest.mpd")
    print("  ├── content-id_1080p.mp4")
    print("  ├── content-id_720p.mp4") 
    print("  ├── content-id_480p.mp4")
    print("  ├── content-id_360p.mp4")
    print("  ├── content-id_240p.mp4")
    print("  ├── content-id_audio.mp4")
    print("  └── thumbnails/")
    print("      ├── content-id_thumb.00001.jpg")
    print("      └── content-id_thumb.00002.jpg")
    print("\nManifest URL: https://contents-stage.keepsdev.com/clients/workspace-id/mpdash/content-id/manifest.mpd")


def main():
    """Executa todos os testes"""
    print("🚀 Iniciando testes do AWS Media Converter DASH\n")
    
    tests = [
        test_media_converter_config,
        test_media_converter_client,
        test_file_conversion_logic,
        test_s3_key_extraction
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Erro inesperado no teste {test.__name__}: {str(e)}")
    
    print(f"\n📊 Resultado: {passed}/{total} testes passaram")
    
    if passed == total:
        print("🎉 Todos os testes passaram! O Media Converter está configurado corretamente.")
        show_example_conversion()
    else:
        print("⚠️  Alguns testes falharam. Verifique as configurações.")
        print("\n📝 Configurações necessárias:")
        print("- AWS_MEDIA_CONVERTER_ROLE_ARN")
        print("- AWS_ACCESS_KEY_ID")
        print("- AWS_SECRET_ACCESS_KEY")
        print("- AWS_REGION_NAME")


if __name__ == "__main__":
    main()
