#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test script to verify Celery tasks are properly registered
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from config.celery import app


def test_registered_tasks():
    """Test if our tasks are properly registered with Celery"""
    print("Testing Celery task registration...")
    
    # Get all registered tasks
    registered_tasks = list(app.tasks.keys())
    
    print(f"\nTotal registered tasks: {len(registered_tasks)}")
    
    # Check for our specific tasks
    required_tasks = [
        'check_media_conversion_status',
        'check_single_conversion_status'
    ]
    
    print("\nChecking for Media Converter tasks:")
    for task_name in required_tasks:
        if task_name in registered_tasks:
            print(f"✓ {task_name} - REGISTERED")
        else:
            print(f"✗ {task_name} - NOT FOUND")
    
    print("\nAll registered tasks:")
    for task in sorted(registered_tasks):
        if not task.startswith('celery.'):
            print(f"  - {task}")


def test_task_import():
    """Test if we can import the tasks directly"""
    print("\nTesting direct task imports...")
    
    try:
        from tasks.media_converter.check_conversion_status import check_single_conversion_status
        print("✓ check_single_conversion_status imported successfully")
        
        from tasks.media_converter.check_conversion_status import check_media_conversion_status
        print("✓ check_media_conversion_status imported successfully")
        
    except ImportError as e:
        print(f"✗ Import error: {e}")


if __name__ == "__main__":
    test_task_import()
    test_registered_tasks()
