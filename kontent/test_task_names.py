#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test script to verify task names are consistent
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from config.celery import app
from task_names import CHECK_SINGLE_CONVERSION_STATUS

# Force import to register the task
import tasks.media_converter


def test_task_consistency():
    """Test if task names are consistent between constants and registered tasks"""
    print("Testing task name consistency...")
    
    # Get all registered tasks
    registered_tasks = list(app.tasks.keys())
    
    print(f"\nConstant value: {CHECK_SINGLE_CONVERSION_STATUS}")
    
    # Check if the constant matches the registered task
    if CHECK_SINGLE_CONVERSION_STATUS in registered_tasks:
        print(f"✓ {CHECK_SINGLE_CONVERSION_STATUS} - REGISTERED CORRECTLY")
    else:
        print(f"✗ {CHECK_SINGLE_CONVERSION_STATUS} - NOT FOUND IN REGISTERED TASKS")
        print("Registered tasks:")
        for task in sorted(registered_tasks):
            if not task.startswith('celery.'):
                print(f"  - {task}")
    
    # Test import
    try:
        from tasks.media_converter.check_conversion_status import check_single_conversion_status
        print("✓ Task function imported successfully")
    except ImportError as e:
        print(f"✗ Import error: {e}")


if __name__ == "__main__":
    test_task_consistency()
