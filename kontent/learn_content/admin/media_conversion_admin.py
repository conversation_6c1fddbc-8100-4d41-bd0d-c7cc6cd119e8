# -*- coding: utf-8 -*-

from django.contrib import admin
from learn_content.models.media_conversion import MediaConversion


@admin.register(MediaConversion)
class MediaConversionAdmin(admin.ModelAdmin):
    list_display = [
        'id',
        'content_name',
        'conversion_type',
        'status',
        'progress',
        'aws_job_id',
        'created_date',
        'job_duration'
    ]
    
    list_filter = [
        'status',
        'conversion_type',
        'created_date',
        'job_started_at',
        'job_finished_at'
    ]
    
    search_fields = [
        'content__name',
        'aws_job_id',
        'workspace_id'
    ]
    
    readonly_fields = [
        'id',
        'created_date',
        'updated_date',
        'job_duration'
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'id',
                'content',
                'conversion_type',
                'workspace_id'
            )
        }),
        ('Job Details', {
            'fields': (
                'aws_job_id',
                'status',
                'progress',
                'job_started_at',
                'job_finished_at',
                'job_duration'
            )
        }),
        ('URLs', {
            'fields': (
                'input_url',
                'output_url'
            )
        }),
        ('Error Handling', {
            'fields': (
                'error_message',
                'error_code',
                'retry_count',
                'max_retries'
            ),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': (
                'created_date',
                'updated_date'
            ),
            'classes': ('collapse',)
        })
    )
    
    def content_name(self, obj):
        return obj.content.name
    content_name.short_description = 'Content Name'
    content_name.admin_order_field = 'content__name'
    
    def job_duration(self, obj):
        if obj.job_started_at and obj.job_finished_at:
            duration = obj.job_finished_at - obj.job_started_at
            return f"{duration.total_seconds():.0f}s"
        return "-"
    job_duration.short_description = 'Duration'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('content')
