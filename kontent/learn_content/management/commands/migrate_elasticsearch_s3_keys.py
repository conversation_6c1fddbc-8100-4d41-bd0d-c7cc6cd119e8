from django.core.management.base import BaseCommand
from learn_content.models import LearnContent
from learn_content.models.documents import LearnContentDocument


class Command(BaseCommand):
    help = 'Migrate S3 keys to Elasticsearch documents'

    def add_arguments(self, parser):
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Number of records to process in each batch'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes'
        )

    def handle(self, *args, **options):
        batch_size = options['batch_size']
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Get all content with S3 keys
        contents = LearnContent.objects.filter(s3_key__isnull=False)
        total_count = contents.count()
        
        self.stdout.write(f'Found {total_count} content records with S3 keys')
        
        updated_count = 0
        error_count = 0
        
        for i in range(0, total_count, batch_size):
            batch = contents[i:i + batch_size]
            
            for content in batch:
                try:
                    if dry_run:
                        self.stdout.write(f'Would update ES doc for content {content.id} with s3_key: {content.s3_key}')
                        updated_count += 1
                    else:
                        # Try to get existing document
                        try:
                            doc = LearnContentDocument.get(content.id)
                            doc.s3_key = content.s3_key
                            doc.save()
                            updated_count += 1
                            
                            if updated_count % 50 == 0:
                                self.stdout.write(f'Updated {updated_count}/{total_count} documents')
                                
                        except Exception as e:
                            self.stdout.write(
                                self.style.WARNING(f'Could not update ES doc for content {content.id}: {str(e)}')
                            )
                            error_count += 1
                            
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error processing content {content.id}: {str(e)}')
                    )
                    error_count += 1
        
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(f'DRY RUN: Would update {updated_count} documents, {error_count} errors')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully updated {updated_count} documents, {error_count} errors')
            )
