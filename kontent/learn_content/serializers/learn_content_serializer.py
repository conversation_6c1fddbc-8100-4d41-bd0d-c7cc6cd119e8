# -*- coding: utf-8 -*-

from rest_framework import serializers
from learn_content.models.learn_content import LearnContent


class LearnContentSerializer(serializers.ModelSerializer):

    class Meta:
        model = LearnContent
        fields = '__all__'


class LearnContentUpdateSerializer(serializers.Serializer):
    duration = serializers.IntegerField()

    def update(self, instance, validated_data):
        raise NotImplementedError()

    def create(self, validated_data):
        raise NotImplementedError()


class ScormSerializer(serializers.ModelSerializer):
    order = serializers.IntegerField()

    class Meta:
        model = LearnContent
        fields = '__all__'


class LearnContentListSerializer(serializers.ModelSerializer):

    class Meta:
        model = LearnContent
        fields = '__all__'
        depth = 1

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        ret['url'] = instance.full_url
        return ret


class ContentTypeSerializer(serializers.Serializer):
    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass

    id = serializers.UUIDField()
    name = serializers.CharField()


class ContentTagEntitySerializer(serializers.Serializer):
    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass

    text = serializers.CharField()
    relevance = serializers.FloatField()
    count = serializers.IntegerField()


class LearnContentDocumentListSerializer(serializers.Serializer):
    def update(self, instance, validated_data):
        pass

    def create(self, validated_data):
        pass

    name = serializers.CharField()
    description = serializers.CharField()
    category = serializers.DictField()
    url = serializers.URLField()
    content_type = ContentTypeSerializer()
    created_date = serializers.DateTimeField()
    updated_date = serializers.DateTimeField()

    content_transcript = serializers.CharField()

    duration = serializers.FloatField()
    points = serializers.FloatField()
    slides_count = serializers.IntegerField()
    tag = serializers.ListField(child=ContentTagEntitySerializer(), allow_empty=False)
    summary = serializers.CharField()

    entities = serializers.ListField(child=ContentTagEntitySerializer(), allow_empty=False)
    language = serializers.CharField()
