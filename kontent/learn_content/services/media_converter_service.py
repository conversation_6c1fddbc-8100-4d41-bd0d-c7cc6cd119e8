# -*- coding: utf-8 -*-
import logging
from typing import Optional
from django.utils import timezone

from config import settings
from learn_content.models import LearnContent
from learn_content.models.media_conversion import MediaConversion
from utils.aws.aws_media_converter import AwsMediaConverter
from config.celery import app
from task_names import CHECK_SINGLE_CONVERSION_STATUS

logger = logging.getLogger(__name__)


class MediaConverterService:
    def __init__(self, media_converter_client: AwsMediaConverter):
        self.media_converter_client = media_converter_client

    def convert_to_dash(self, content: LearnContent) -> bool:
        try:
            if not content.s3_key:
                logger.error(f"No S3 key available for content {content.id}")
                return False
            
            s3_key = content.s3_key

            # Create or get existing conversion record
            conversion, created = MediaConversion.objects.get_or_create(
                content=content,
                defaults={'status': 'SUBMITTED'}
            )

            if not created and conversion.status == 'COMPLETE':
                logger.info(f"Content {content.id} already converted")
                return True

            # Submit conversion job
            job_response = self.media_converter_client.convert_to_dash(
                input_s3_key=s3_key,
                output_s3_key_prefix=f"clients/{s3_key.split('/')[1]}/mpdash/{content.id}/" if s3_key.startswith('clients/') else f"mpdash/{content.id}/"
            )

            if job_response and 'job_id' in job_response:
                conversion.aws_job_id = job_response['job_id']
                conversion.status = 'SUBMITTED'
                conversion.save()

                # Schedule status check
                app.send_task(
                    CHECK_SINGLE_CONVERSION_STATUS,
                    args=[str(conversion.id)],
                    countdown=30
                )

                logger.info(f"Conversion job submitted for content {content.id}. Job ID: {job_response['job_id']}")
                return True
            else:
                logger.error(f"Failed to submit conversion job for content {content.id}")
                return False

        except Exception as e:
            logger.error(f"Error converting content {content.id}: {str(e)}")
            return False

    def check_conversion_status(self, conversion: MediaConversion) -> bool:
        try:
            if not conversion.aws_job_id:
                logger.error(f"No AWS job ID for conversion {conversion.id}")
                return False

            status_info = self.media_converter_client.get_job_status(conversion.aws_job_id)
            
            if not status_info:
                logger.error(f"Could not get status for job {conversion.aws_job_id}")
                return False

            status = status_info.get("status")
            
            if status == "COMPLETE":
                self._handle_conversion_complete(conversion)
            elif status in ["ERROR", "CANCELED"]:
                self._handle_conversion_failed(conversion, status_info)
            elif status == "SUBMITTED":
                # Still processing, reschedule check
                app.send_task(
                    CHECK_SINGLE_CONVERSION_STATUS,
                    args=[str(conversion.id)],
                    countdown=60
                )
                logger.info(f"Conversion still processing for content {conversion.content.id}")

            return True

        except Exception as e:
            logger.error(f"Error checking conversion status for {conversion.id}: {str(e)}")
            return False

    def _handle_conversion_complete(self, conversion: MediaConversion):
        try:
            conversion.status = "COMPLETE"
            conversion.updated_date = timezone.now()
            
            output_s3_key = self._build_output_s3_key(conversion)

            if output_s3_key:
                conversion.output_s3_key = output_s3_key
                # Also store full URL for compatibility
                conversion.output_url = f"{settings.AWS_STREAMING_URL}/{output_s3_key}"
                conversion.save()
                logger.info(f"Conversion completed for content {conversion.content.id}. S3 Key: {output_s3_key}")
            else:
                logger.error(f"Could not determine output S3 key for content {conversion.content.id}")

        except Exception as e:
            logger.error(f"Failed to handle conversion completion for content {conversion.content.id}: {str(e)}")

    def _handle_conversion_failed(self, conversion: MediaConversion, status_info: dict):
        conversion.status = "ERROR"
        conversion.save()
        error_message = status_info.get("error_message", "Unknown error")
        logger.error(f"Conversion failed for content {conversion.content.id}: {error_message}")

    def _build_output_s3_key(self, conversion: MediaConversion) -> Optional[str]:
        try:
            # Extract workspace_id from content S3 key
            if conversion.content.s3_key:
                parts = conversion.content.s3_key.split('/')
                if len(parts) >= 3 and parts[0] == 'clients':
                    workspace_id = parts[1]
                    return f"clients/{workspace_id}/mpdash/{conversion.content.id}/manifest.mpd"

            return f"mpdash/{conversion.content.id}/manifest.mpd"

        except Exception as e:
            logger.error(f"Failed to build output S3 key for content {conversion.content.id}: {str(e)}")
            return None


def create_media_converter_service() -> MediaConverterService:
    media_converter_client = AwsMediaConverter(
        access_key=settings.AWS_ACCESS_KEY_ID,
        secret_key=settings.AWS_SECRET_ACCESS_KEY,
        region=settings.AWS_REGION_NAME,
    )
    
    return MediaConverterService(media_converter_client)
