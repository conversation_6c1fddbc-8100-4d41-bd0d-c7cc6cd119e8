# -*- coding: utf-8 -*-
import logging
from typing import Optional

from learn_content.models import LearnContent
from utils.aws.aws_media_converter import AwsMediaConverter
from config.celery import app
from task_names import CHECK_SINGLE_CONVERSION_STATUS

logger = logging.getLogger(__name__)


class MediaConverterService:
    """
    Service to handle video conversion using AWS Media Converter
    """

    def __init__(self, media_converter_client: AwsMediaConverter):
        self.media_converter = media_converter_client

    def convert_video_if_needed(self, content: LearnContent, workspace_id: str = None) -> bool:
        """
        Convert video file if it meets the criteria for conversion

        Args:
            content: LearnContent instance
            workspace_id: Workspace ID for organizing files

        Returns:
            Boolean indicating if conversion was initiated
        """
        try:
            # Check if file should be converted
            if not self.media_converter.should_convert_file(
                content.file_mime_type,
                content.file_size
            ):
                logger.info(f"File {content.id} does not need conversion. MIME: {content.file_mime_type}")
                return False

            # Check if already being converted
            if content.media_converter_job_id:
                logger.info(f"File {content.id} already has conversion job: {content.media_converter_job_id}")
                return False

            # Extract S3 key from URL
            s3_key = self._extract_s3_key_from_url(content.url)
            if not s3_key:
                logger.error(f"Could not extract S3 key from URL: {content.url}")
                return False

            # Start DASH conversion
            result = self.media_converter.convert_mp4_to_dash(
                input_s3_key=s3_key,
                content_id=str(content.id),
                workspace_id=workspace_id
            )

            # Update content with job ID only
            content.media_converter_job_id = result["job_id"]
            content.save()

            logger.info(f"Started Media Converter DASH job {result['job_id']} for content {content.id}")

            # Schedule task to monitor job completion
            # Schedule the first check in 60 seconds
            app.send_task(
                CHECK_SINGLE_CONVERSION_STATUS,
                args=(str(content.id),),
                countdown=60
            )

            return True

        except Exception as e:
            logger.error(f"Failed to start video conversion for content {content.id}: {str(e)}")
            return False

    def update_conversion_status(self, content: LearnContent) -> Optional[str]:
        """
        Check and update the conversion status for a content
        
        Args:
            content: LearnContent instance with media_converter_job_id
            
        Returns:
            Job status or None if error
        """
        if not content.media_converter_job_id:
            return None

        try:
            status_info = self.media_converter.get_job_status(content.media_converter_job_id)
            job_status = status_info["status"]

            if job_status == "COMPLETE":
                # Job completed successfully
                self._handle_conversion_complete(content)
            elif job_status in ["ERROR", "CANCELED"]:
                # Job failed
                self._handle_conversion_failed(content, status_info)

            return job_status

        except Exception as e:
            logger.error(f"Failed to check conversion status for content {content.id}: {str(e)}")
            return None

    def _handle_conversion_complete(self, content: LearnContent):
        """Handle successful conversion completion"""
        try:
            # Build the converted file URL
            # This assumes the output follows the pattern from the converter
            converted_url = self._build_converted_url(content)
            
            if converted_url:
                content.converted_url = converted_url
                content.save()
                logger.info(f"Conversion completed for content {content.id}. URL: {converted_url}")
            else:
                logger.error(f"Could not determine converted URL for content {content.id}")

        except Exception as e:
            logger.error(f"Failed to handle conversion completion for content {content.id}: {str(e)}")

    def _handle_conversion_failed(self, content: LearnContent, status_info: dict):
        """Handle failed conversion"""
        error_message = status_info.get("error_message", "Unknown error")
        logger.error(f"Conversion failed for content {content.id}: {error_message}")
        
        # Optionally, you could set a flag or clear the job ID
        # content.media_converter_job_id = None
        # content.save()

    def _extract_s3_key_from_url(self, s3_url: str) -> Optional[str]:
        """
        Extract S3 key from S3 URL
        
        Args:
            s3_url: Full S3 URL
            
        Returns:
            S3 key or None if extraction fails
        """
        try:
            # Handle different S3 URL formats
            if s3_url.startswith('https://'):
                # Format: https://bucket.s3.region.amazonaws.com/key
                # or https://s3.region.amazonaws.com/bucket/key
                parts = s3_url.split('/')
                if '.s3.' in parts[2]:
                    # bucket.s3.region.amazonaws.com format
                    return '/'.join(parts[3:])
                elif 's3.' in parts[2]:
                    # s3.region.amazonaws.com/bucket/key format
                    return '/'.join(parts[4:])
            elif s3_url.startswith('s3://'):
                # Format: s3://bucket/key
                return s3_url.split('/', 3)[3]
                
            return None
            
        except Exception as e:
            logger.error(f"Failed to extract S3 key from URL {s3_url}: {str(e)}")
            return None



    def _build_converted_url(self, content: LearnContent) -> Optional[str]:
        """
        Build the URL for the converted DASH manifest

        Args:
            content: LearnContent instance

        Returns:
            DASH manifest URL or None
        """
        try:
            # Extract S3 key and workspace_id from original URL
            s3_key = self._extract_s3_key_from_url(content.url)
            if not s3_key:
                return None

            # Extract workspace_id from S3 key path
            # Expected format: clients/{workspace_id}/general/{file_id}.mp4
            parts = s3_key.split('/')
            if len(parts) >= 3 and parts[0] == 'clients':
                workspace_id = parts[1]
                # Build DASH manifest URL
                return f"https://contents-stage.keepsdev.com/clients/{workspace_id}/mpdash/{content.id}/manifest.mpd"

            return None

        except Exception as e:
            logger.error(f"Failed to build converted URL for content {content.id}: {str(e)}")
            return None


def create_media_converter_service() -> MediaConverterService:
    """
    Factory function to create MediaConverterService with proper dependencies
    """
    from config import settings
    
    # Initialize Media Converter client
    media_converter_client = AwsMediaConverter(
        access_key=settings.AWS_ACCESS_KEY_ID,
        secret_key=settings.AWS_SECRET_ACCESS_KEY,
        region=settings.AWS_REGION_NAME,
        input_bucket=settings.AWS_MEDIA_CONVERTER_INPUT_BUCKET,
        output_bucket=settings.AWS_MEDIA_CONVERTER_OUTPUT_BUCKET,
        role_arn=settings.AWS_MEDIA_CONVERTER_ROLE_ARN,
        queue_arn=settings.AWS_MEDIA_CONVERTER_QUEUE_ARN,
        account_id=settings.AWS_ACCOUNT_ID
    )
    
    return MediaConverterService(media_converter_client)
