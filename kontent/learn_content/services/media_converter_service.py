# -*- coding: utf-8 -*-
import logging
from typing import Optional
from django_injector import inject

from config import settings
from learn_content.models import LearnContent
from learn_content.models.media_conversion import MediaConversion
from utils.aws.aws_media_converter import AwsMediaConverter
from config.celery import app
from task_names import CHECK_SINGLE_CONVERSION_STATUS

logger = logging.getLogger(__name__)


class MediaConverterService:

    @inject
    def __init__(self, media_converter_client: AwsMediaConverter):
        self.media_converter = media_converter_client

    def convert_video_if_needed(self, content: LearnContent, workspace_id: str = None) -> bool:
        try:
            if not self.media_converter.should_convert_file(content.file_mime_type, content.file_size):
                logger.info(f"File {content.id} does not need conversion. MIME: {content.file_mime_type}")
                return False

            if content.has_active_conversion():
                logger.info(f"File {content.id} already has active conversion")
                return False

            if not content.s3_key:
                logger.error(f"No S3 key available for content {content.id}")
                return False

            s3_key = content.s3_key

            result = self.media_converter.convert_mp4_to_dash(
                input_s3_key=s3_key,
                content_id=str(content.id),
                workspace_id=workspace_id
            )

            # Create MediaConversion record
            conversion, created = MediaConversion.objects.get_or_create(
                content=content,
                defaults={
                    'aws_job_id': result["job_id"],
                    'status': "SUBMITTED"
                }
            )

            logger.info(f"Started Media Converter DASH job {result['job_id']} for content {content.id}")

            app.send_task(CHECK_SINGLE_CONVERSION_STATUS, args=(str(content.id),))

            return True

        except Exception as e:
            logger.error(f"Failed to start video conversion for content {content.id}: {str(e)}")
            return False

    def update_conversion_status(self, content: LearnContent) -> Optional[str]:
        conversion = content.media_conversion
        if not conversion:
            return None

        if not conversion or not conversion.aws_job_id:
            return None

        try:
            status_info = self.media_converter.get_job_status(conversion.aws_job_id)
            aws_status = status_info["status"]

            # Map AWS status to our simplified status
            if aws_status == "COMPLETE":
                conversion.status = "COMPLETE"
                self._handle_conversion_complete(conversion)
            elif aws_status in ["ERROR", "CANCELED"]:
                conversion.status = aws_status
                self._handle_conversion_failed(conversion, status_info)
            else:
                # All intermediate AWS statuses (SUBMITTED, INPUT_INFORMATION, PROGRESSING, STATUS_UPDATE)
                # are mapped to SUBMITTED in our simplified model
                conversion.status = "SUBMITTED"

            conversion.save()
            return conversion.status

        except Exception as e:
            logger.error(f"Failed to check conversion status for content {content.id}: {str(e)}")
            return None

    def _handle_conversion_complete(self, conversion: MediaConversion):
        try:
            s3_key = self._build_output_s3_key(conversion)

            if s3_key:
                conversion.s3_key = s3_key
                conversion.save()
                logger.info(f"Conversion completed for content {conversion.content.id}. S3 Key: {s3_key}")
            else:
                logger.error(f"Could not determine output S3 key for content {conversion.content.id}")

        except Exception as e:
            logger.error(f"Failed to handle conversion completion for content {conversion.content.id}: {str(e)}")

    def _handle_conversion_failed(self, conversion: MediaConversion, status_info: dict):
        error_message = status_info.get("error_message", "Unknown error")
        logger.error(f"Conversion failed for content {conversion.content.id}: {error_message}")

    def _build_output_s3_key(self, conversion: MediaConversion) -> Optional[str]:
        try:
            # Extract workspace_id from content S3 key
            if conversion.content.s3_key:
                parts = conversion.content.s3_key.split('/')
                if len(parts) >= 3 and parts[0] == 'clients':
                    workspace_id = parts[1]
                    return f"clients/{workspace_id}/mpdash/{conversion.content.id}/manifest.mpd"

            return f"mpdash/{conversion.content.id}/manifest.mpd"

        except Exception as e:
            logger.error(f"Failed to build output S3 key for content {conversion.content.id}: {str(e)}")
            return None


def create_media_converter_service() -> MediaConverterService:
    from di import Container
    container = Container()
    return MediaConverterService(container.aws_media_converter_client())
