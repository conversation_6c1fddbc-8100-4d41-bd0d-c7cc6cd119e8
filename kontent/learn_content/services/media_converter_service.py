# -*- coding: utf-8 -*-
import logging
from typing import Op<PERSON>

from config import settings
from learn_content.models import LearnContent
from learn_content.models.media_conversion import MediaConversion
from utils.aws.aws_media_converter import AwsMediaConverter
from config.celery import app
from task_names import CHECK_SINGLE_CONVERSION_STATUS

logger = logging.getLogger(__name__)


class MediaConverterService:

    def __init__(self, media_converter_client: AwsMediaConverter):
        self.media_converter = media_converter_client

    def convert_video_if_needed(self, content: LearnContent, workspace_id: str = None) -> bool:
        try:
            if not self.media_converter.should_convert_file(content.file_mime_type, content.file_size):
                logger.info(f"File {content.id} does not need conversion. MIME: {content.file_mime_type}")
                return False

            if content.has_active_conversion():
                logger.info(f"File {content.id} already has active conversion")
                return False

            if not content.s3_key:
                logger.error(f"No S3 key available for content {content.id}")
                return False

            s3_key = content.s3_key

            result = self.media_converter.convert_mp4_to_dash(
                input_s3_key=s3_key,
                content_id=str(content.id),
                workspace_id=workspace_id
            )

            # Create MediaConversion record
            conversion, created = MediaConversion.objects.get_or_create(
                content=content,
                defaults={
                    'aws_job_id': result["job_id"],
                    'status': "SUBMITTED"
                }
            )

            logger.info(f"Started Media Converter DASH job {result['job_id']} for content {content.id}")

            app.send_task(CHECK_SINGLE_CONVERSION_STATUS, args=(str(content.id),))

            return True

        except Exception as e:
            logger.error(f"Failed to start video conversion for content {content.id}: {str(e)}")
            return False

    def update_conversion_status(self, content: LearnContent) -> Optional[str]:
        conversion = content.media_conversion
        if not conversion or not conversion.aws_job_id:
            return None

        try:
            status_info = self.media_converter.get_job_status(conversion.aws_job_id)
            aws_status = status_info["status"]

            self._update_conversion_based_on_aws_status(conversion, aws_status, status_info)
            conversion.save()
            return conversion.status

        except Exception as e:
            logger.error(f"Failed to check conversion status for content {content.id}: {str(e)}")
            return None

    def _update_conversion_based_on_aws_status(self, conversion: MediaConversion, aws_status: str, status_info: dict):
        """Update conversion status based on AWS job status"""
        if aws_status == "COMPLETE":
            self._handle_conversion_complete(conversion, status_info)
        elif aws_status in ["ERROR", "CANCELED"]:
            self._handle_conversion_failed(conversion, status_info)
        else:
            self._handle_conversion_in_progress(conversion)

    def _handle_conversion_complete(self, conversion: MediaConversion, status_info: dict):
        """Handle completed conversion"""
        conversion.status = "COMPLETE"

        output_s3_key = status_info.get("output_s3_key")
        if output_s3_key:
            conversion.s3_key = output_s3_key
            logger.info(f"Conversion completed for content {conversion.content.id}. S3 Key: {conversion.s3_key}")
        else:
            logger.warning(f"Conversion completed but no output S3 key found for content {conversion.content.id}")

    def _handle_conversion_in_progress(self, conversion: MediaConversion):
        """Handle conversion that is still in progress"""
        # All intermediate AWS statuses (SUBMITTED, INPUT_INFORMATION, PROGRESSING, STATUS_UPDATE)
        # are mapped to SUBMITTED in our simplified model
        conversion.status = "SUBMITTED"

    def _handle_conversion_failed(self, conversion: MediaConversion, status_info: dict):
        error_message = status_info.get("error_message", "Unknown error")
        logger.error(f"Conversion failed for content {conversion.content.id}: {error_message}")