# -*- coding: utf-8 -*-
import logging
from typing import Optional

from learn_content.models import LearnContent
from utils.aws.aws_media_converter import AwsMediaConverter
from config.celery import app
from task_names import CHECK_SINGLE_CONVERSION_STATUS

logger = logging.getLogger(__name__)


class MediaConverterService:

    def __init__(self, media_converter_client: AwsMediaConverter):
        self.media_converter = media_converter_client

    def convert_video_if_needed(self, content: LearnContent, workspace_id: str = None) -> bool:
        try:
            if not self.media_converter.should_convert_file(content.file_mime_type, content.file_size):
                logger.info(f"File {content.id} does not need conversion. MIME: {content.file_mime_type}")
                return False

            if content.media_converter_job_id:
                logger.info(f"File {content.id} already has conversion job: {content.media_converter_job_id}")
                return False

            s3_key = self._extract_s3_key_from_url(content.url)
            if not s3_key:
                logger.error(f"Could not extract S3 key from URL: {content.url}")
                return False

            result = self.media_converter.convert_mp4_to_dash(
                input_s3_key=s3_key,
                content_id=str(content.id),
                workspace_id=workspace_id
            )

            content.media_converter_job_id = result["job_id"]
            content.save()

            logger.info(f"Started Media Converter DASH job {result['job_id']} for content {content.id}")

            app.send_task(CHECK_SINGLE_CONVERSION_STATUS, args=(str(content.id),))

            return True

        except Exception as e:
            logger.error(f"Failed to start video conversion for content {content.id}: {str(e)}")
            return False

    def update_conversion_status(self, content: LearnContent) -> Optional[str]:
        if not content.media_converter_job_id:
            logger.warning(f"No job ID found for content {content.id}")
            return None

        try:
            logger.info(f"Checking job status for content {content.id}, job_id: {content.media_converter_job_id}")
            status_info = self.media_converter.get_job_status(content.media_converter_job_id)
            job_status = status_info["status"]
            progress = status_info.get("progress", 0)

            logger.info(f"Job status for content {content.id}: {job_status}, progress: {progress}%")

            if job_status == "COMPLETE":
                logger.info(f"Job completed for content {content.id}, handling completion")
                self._handle_conversion_complete(content)
            elif job_status in ["ERROR", "CANCELED"]:
                logger.error(f"Job failed for content {content.id} with status: {job_status}")
                self._handle_conversion_failed(content, status_info)
            else:
                logger.info(f"Job still in progress for content {content.id}: {job_status}")

            return job_status

        except Exception as e:
            logger.error(f"Failed to check conversion status for content {content.id}: {str(e)}")
            return None

    def _handle_conversion_complete(self, content: LearnContent):
        try:
            logger.info(f"Building converted URL for content {content.id}")
            logger.info(f"Content original URL: {content.url}")

            converted_url = self._build_converted_url(content)
            logger.info(f"Built converted URL for content {content.id}: {converted_url}")

            if converted_url:
                content.converted_url = converted_url
                content.save()
                logger.info(f"Conversion completed for content {content.id}. URL saved: {converted_url}")
            else:
                logger.error(f"Could not determine converted URL for content {content.id}")

        except Exception as e:
            logger.error(f"Failed to handle conversion completion for content {content.id}: {str(e)}")

    def _handle_conversion_failed(self, content: LearnContent, status_info: dict):
        error_message = status_info.get("error_message", "Unknown error")
        logger.error(f"Conversion failed for content {content.id}: {error_message}")

    def _extract_s3_key_from_url(self, s3_url: str) -> Optional[str]:
        try:
            if s3_url.startswith('https://'):
                parts = s3_url.split('/')
                if '.s3.' in parts[2]:
                    return '/'.join(parts[3:])
                elif 's3.' in parts[2]:
                    return '/'.join(parts[4:])
            elif s3_url.startswith('s3://'):
                return s3_url.split('/', 3)[3]

            return None

        except Exception as e:
            logger.error(f"Failed to extract S3 key from URL {s3_url}: {str(e)}")
            return None



    def _build_converted_url(self, content: LearnContent) -> Optional[str]:
        try:
            logger.info(f"Extracting S3 key from URL: {content.url}")
            s3_key = self._extract_s3_key_from_url(content.url)
            logger.info(f"Extracted S3 key: {s3_key}")

            if not s3_key:
                logger.error(f"Could not extract S3 key from URL: {content.url}")
                return None

            parts = s3_key.split('/')
            logger.info(f"S3 key parts: {parts}")

            if len(parts) >= 3 and parts[0] == 'clients':
                workspace_id = parts[1]
                logger.info(f"Found workspace_id: {workspace_id}")

                from config import settings
                cdn_base_url = settings.AWS_MEDIA_CONVERTER_CDN_BASE_URL
                logger.info(f"Using CDN base URL: {cdn_base_url}")

                converted_url = f"{cdn_base_url}/clients/{workspace_id}/mpdash/{content.id}/manifest.mpd"
                logger.info(f"Built converted URL: {converted_url}")
                return converted_url
            else:
                logger.warning(f"S3 key does not match expected pattern (clients/workspace/...). Parts: {parts}")

            return None

        except Exception as e:
            logger.error(f"Failed to build converted URL for content {content.id}: {str(e)}")
            return None


def create_media_converter_service() -> MediaConverterService:
    from config import settings

    media_converter_client = AwsMediaConverter(
        access_key=settings.AWS_ACCESS_KEY_ID,
        secret_key=settings.AWS_SECRET_ACCESS_KEY,
        region=settings.AWS_REGION_NAME,
        input_bucket=settings.AWS_MEDIA_CONVERTER_INPUT_BUCKET,
        output_bucket=settings.AWS_MEDIA_CONVERTER_OUTPUT_BUCKET,
        account_id=settings.AWS_ACCOUNT_ID
    )
    return MediaConverterService(media_converter_client)
