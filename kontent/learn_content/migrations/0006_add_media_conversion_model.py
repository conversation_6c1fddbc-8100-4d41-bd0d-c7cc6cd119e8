# Generated manually for MediaConversion model

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('learn_content', '0005_auto_20250701_1907'),
    ]

    operations = [
        migrations.CreateModel(
            name='MediaConversion',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('conversion_type', models.CharField(choices=[('MP4_TO_DASH', 'MP4 to DASH'), ('VIDEO_COMPRESSION', 'Video Compression'), ('AUDIO_EXTRACTION', 'Audio Extraction')], default='MP4_TO_DASH', max_length=50, verbose_name='Conversion Type')),
                ('aws_job_id', models.CharField(blank=True, max_length=200, null=True, verbose_name='AWS Job ID')),
                ('status', models.CharField(choices=[('SUBMITTED', 'Submitted'), ('INPUT_INFORMATION', 'Reading Input'), ('PROGRESSING', 'Processing'), ('STATUS_UPDATE', 'Status Update'), ('COMPLETE', 'Complete'), ('ERROR', 'Error'), ('CANCELED', 'Canceled')], default='SUBMITTED', max_length=50, verbose_name='Status')),
                ('progress', models.IntegerField(default=0, help_text='Conversion progress percentage', verbose_name='Progress (%)')),
                ('input_url', models.URLField(help_text='Original file URL', max_length=500, verbose_name='Input URL')),
                ('output_url', models.URLField(blank=True, help_text='Converted file URL (DASH manifest)', max_length=500, null=True, verbose_name='Output URL')),
                ('workspace_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='Workspace ID')),
                ('job_started_at', models.DateTimeField(blank=True, null=True, verbose_name='Job Started At')),
                ('job_finished_at', models.DateTimeField(blank=True, null=True, verbose_name='Job Finished At')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='Error Message')),
                ('error_code', models.CharField(blank=True, max_length=100, null=True, verbose_name='Error Code')),
                ('retry_count', models.IntegerField(default=0, verbose_name='Retry Count')),
                ('max_retries', models.IntegerField(default=3, verbose_name='Max Retries')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='media_conversions', to='learn_content.LearnContent', verbose_name='Content')),
            ],
            options={
                'verbose_name': 'Media Conversion',
                'verbose_name_plural': 'Media Conversions',
                'db_table': 'media_conversion',
                'ordering': ['-created_date'],
            },
        ),
        migrations.AddIndex(
            model_name='mediaconversion',
            index=models.Index(fields=['content', 'status'], name='learn_conte_content_f8b7a5_idx'),
        ),
        migrations.AddIndex(
            model_name='mediaconversion',
            index=models.Index(fields=['aws_job_id'], name='learn_conte_aws_job_c7e8f2_idx'),
        ),
        migrations.AddIndex(
            model_name='mediaconversion',
            index=models.Index(fields=['status', 'created_date'], name='learn_conte_status_a9d4b1_idx'),
        ),
    ]
