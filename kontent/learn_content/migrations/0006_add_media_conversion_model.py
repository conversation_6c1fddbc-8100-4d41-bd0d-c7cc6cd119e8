# Generated manually for MediaConversion model

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('learn_content', '0005_auto_20250701_1907'),
    ]

    operations = [
        migrations.CreateModel(
            name='MediaConversion',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('aws_job_id', models.CharField(blank=True, max_length=200, null=True, verbose_name='AWS Job ID')),
                ('status', models.CharField(choices=[('SUBMITTED', 'Submitted'), ('INPUT_INFORMATION', 'Reading Input'), ('PROGRESSING', 'Processing'), ('STATUS_UPDATE', 'Status Update'), ('COMPLETE', 'Complete'), ('ERROR', 'Error'), ('CANCELED', 'Canceled')], default='SUBMITTED', max_length=50, verbose_name='Status')),
                ('output_url', models.URLField(blank=True, help_text='Converted file URL (DASH manifest)', max_length=500, null=True, verbose_name='Output URL')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='media_conversions', to='learn_content.LearnContent', verbose_name='Content')),
            ],
            options={
                'verbose_name': 'Media Conversion',
                'verbose_name_plural': 'Media Conversions',
                'db_table': 'media_conversion',
                'ordering': ['-created_date'],
            },
        ),
        migrations.RemoveField(
            model_name='learncontent',
            name='media_converter_job_id',
        ),
        migrations.RemoveField(
            model_name='learncontent',
            name='converted_url',
        ),
    ]
