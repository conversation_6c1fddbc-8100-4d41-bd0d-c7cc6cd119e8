# -*- coding: utf-8 -*-
# Generated manually to add CASCADE DELETE constraint at database level

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('learn_content', '0005_auto_20250703_2350'),
    ]

    operations = [
        # Remove the existing foreign key constraint
        migrations.RunSQL(
            sql="""
                ALTER TABLE media_conversion 
                DROP CONSTRAINT IF EXISTS media_conversion_content_id_fkey;
            """,
            reverse_sql="""
                -- This will be recreated by the next operation
            """
        ),
        
        # Add the foreign key constraint with CASCADE DELETE
        migrations.RunSQL(
            sql="""
                ALTER TABLE media_conversion 
                ADD CONSTRAINT media_conversion_content_id_fkey 
                FOREIGN KEY (content_id) 
                REFERENCES learn_content(id) 
                ON DELETE CASCADE;
            """,
            reverse_sql="""
                ALTER TABLE media_conversion 
                DROP CONSTRAINT IF EXISTS media_conversion_content_id_fkey;
                
                ALTER TABLE media_conversion 
                ADD CONSTRAINT media_conversion_content_id_fkey 
                FOREIGN KEY (content_id) 
                REFERENCES learn_content(id);
            """
        ),
    ]
