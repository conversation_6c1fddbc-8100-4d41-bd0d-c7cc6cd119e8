# -*- coding: utf-8 -*-
# Generated manually to simplify MediaConversion fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('learn_content', '0006_add_cascade_delete_constraint'),
    ]

    operations = [
        # Rename output_s3_key to s3_key
        migrations.RenameField(
            model_name='mediaconversion',
            old_name='output_s3_key',
            new_name='s3_key',
        ),
        
        # Remove the redundant output_url field
        migrations.RemoveField(
            model_name='mediaconversion',
            name='output_url',
        ),
    ]
