# Migration to move existing media converter data to MediaConversion model

from django.db import migrations


def migrate_media_converter_data(apps, schema_editor):
    """
    Migrate existing media_converter_job_id and converted_url data 
    from LearnContent to MediaConversion model
    """
    LearnContent = apps.get_model('learn_content', 'LearnContent')
    MediaConversion = apps.get_model('learn_content', 'MediaConversion')
    
    # Find all content with media converter data
    contents_with_jobs = LearnContent.objects.filter(
        media_converter_job_id__isnull=False
    ).exclude(media_converter_job_id='')
    
    print(f"Migrating {contents_with_jobs.count()} media conversion records...")
    
    for content in contents_with_jobs:
        # Determine status based on converted_url
        if content.converted_url:
            status = "COMPLETE"
            output_url = content.converted_url
            progress = 100
        else:
            # Job exists but no URL - likely still processing or failed
            status = "PROGRESSING"  # We'll let the task update this
            output_url = None
            progress = 0
        
        # Create MediaConversion record
        MediaConversion.objects.create(
            content=content,
            conversion_type="MP4_TO_DASH",
            aws_job_id=content.media_converter_job_id,
            status=status,
            progress=progress,
            input_url=content.url,
            output_url=output_url,
            # We don't have historical timing data, so leave these null
            job_started_at=None,
            job_finished_at=None,
            error_message=None,
            error_code=None,
            retry_count=0,
            max_retries=3
        )
    
    print(f"Successfully migrated {contents_with_jobs.count()} records to MediaConversion model")


def reverse_migrate_media_converter_data(apps, schema_editor):
    """
    Reverse migration - copy data back to LearnContent fields
    """
    LearnContent = apps.get_model('learn_content', 'LearnContent')
    MediaConversion = apps.get_model('learn_content', 'MediaConversion')
    
    # Get all media conversions
    conversions = MediaConversion.objects.all()
    
    print(f"Reversing migration for {conversions.count()} media conversion records...")
    
    for conversion in conversions:
        # Update the related content
        content = conversion.content
        content.media_converter_job_id = conversion.aws_job_id
        content.converted_url = conversion.output_url
        content.save()
    
    print(f"Successfully reversed migration for {conversions.count()} records")


class Migration(migrations.Migration):

    dependencies = [
        ('learn_content', '0006_add_media_conversion_model'),
    ]

    operations = [
        migrations.RunPython(
            migrate_media_converter_data,
            reverse_migrate_media_converter_data
        ),
    ]
