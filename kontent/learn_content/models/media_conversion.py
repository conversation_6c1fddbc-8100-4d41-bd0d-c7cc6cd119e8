# -*- coding: utf-8 -*-

import uuid
from django.db import models
from learn_content.models.learn_content import Learn<PERSON>ontent

CONVERSION_STATUS_CHOICES = (
    ("SUBMITTED", "Submitted"),
    ("INPUT_INFORMATION", "Reading Input"),
    ("PROGRESSING", "Processing"),
    ("STATUS_UPDATE", "Status Update"),
    ("COMPLETE", "Complete"),
    ("ERROR", "Error"),
    ("CANCELED", "Canceled"),
)

CONVERSION_TYPE_CHOICES = (
    ("MP4_TO_DASH", "MP4 to DASH"),
    ("VIDEO_COMPRESSION", "Video Compression"),
    ("AUDIO_EXTRACTION", "Audio Extraction"),
)


class MediaConversion(models.Model):
    """
    Stores information about media conversion jobs for content
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Relationship
    content = models.ForeignKey(
        LearnContent, 
        verbose_name="Content", 
        on_delete=models.CASCADE,
        related_name="media_conversions"
    )
    
    # Conversion details
    conversion_type = models.CharField(
        verbose_name="Conversion Type",
        max_length=50,
        choices=CONVERSION_TYPE_CHOICES,
        default="MP4_TO_DASH"
    )
    
    # AWS Media Converter job details
    aws_job_id = models.CharField(
        verbose_name="AWS Job ID",
        max_length=200,
        blank=True,
        null=True
    )
    
    status = models.CharField(
        verbose_name="Status",
        max_length=50,
        choices=CONVERSION_STATUS_CHOICES,
        default="SUBMITTED"
    )
    
    progress = models.IntegerField(
        verbose_name="Progress (%)",
        default=0,
        help_text="Conversion progress percentage"
    )
    
    # URLs
    input_url = models.URLField(
        verbose_name="Input URL",
        max_length=500,
        help_text="Original file URL"
    )
    
    output_url = models.URLField(
        verbose_name="Output URL",
        max_length=500,
        blank=True,
        null=True,
        help_text="Converted file URL (DASH manifest)"
    )
    
    # Metadata
    workspace_id = models.CharField(
        verbose_name="Workspace ID",
        max_length=100,
        blank=True,
        null=True
    )
    
    # Job timing
    job_started_at = models.DateTimeField(
        verbose_name="Job Started At",
        blank=True,
        null=True
    )
    
    job_finished_at = models.DateTimeField(
        verbose_name="Job Finished At",
        blank=True,
        null=True
    )
    
    # Error handling
    error_message = models.TextField(
        verbose_name="Error Message",
        blank=True,
        null=True
    )
    
    error_code = models.CharField(
        verbose_name="Error Code",
        max_length=100,
        blank=True,
        null=True
    )
    
    # Retry logic
    retry_count = models.IntegerField(
        verbose_name="Retry Count",
        default=0
    )
    
    max_retries = models.IntegerField(
        verbose_name="Max Retries",
        default=3
    )
    
    # Timestamps
    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)
    
    @property
    def is_complete(self) -> bool:
        """Check if conversion is complete"""
        return self.status == "COMPLETE"
    
    @property
    def is_failed(self) -> bool:
        """Check if conversion failed"""
        return self.status in ["ERROR", "CANCELED"]
    
    @property
    def is_in_progress(self) -> bool:
        """Check if conversion is still in progress"""
        return self.status in ["SUBMITTED", "INPUT_INFORMATION", "PROGRESSING", "STATUS_UPDATE"]
    
    @property
    def can_retry(self) -> bool:
        """Check if conversion can be retried"""
        return self.is_failed and self.retry_count < self.max_retries
    
    @property
    def duration_seconds(self) -> int:
        """Get conversion duration in seconds"""
        if self.job_started_at and self.job_finished_at:
            return int((self.job_finished_at - self.job_started_at).total_seconds())
        return 0
    
    def __str__(self):
        return f"MediaConversion {self.id} - {self.content.name} ({self.status})"
    
    class Meta:
        app_label = 'learn_content'
        verbose_name = "Media Conversion"
        verbose_name_plural = "Media Conversions"
        db_table = 'media_conversion'
        ordering = ['-created_date']
        indexes = [
            models.Index(fields=['content', 'status']),
            models.Index(fields=['aws_job_id']),
            models.Index(fields=['status', 'created_date']),
        ]
