# -*- coding: utf-8 -*-

import uuid
from django.db import models
from learn_content.models.learn_content import Learn<PERSON>ontent

CONVERSION_STATUS_CHOICES = (
    ("SUBMITTED", "Submitted"),
    ("COMPLETE", "Complete"),
    ("ERROR", "Error"),
    ("CANCELED", "Canceled"),
)


class MediaConversion(models.Model):
    """
    Stores information about media conversion jobs for content
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Relationship
    content = models.OneToOneField(
        LearnContent,
        verbose_name="Content",
        on_delete=models.CASCADE,
        related_name="media_conversion"
    )

    # AWS Media Converter job details
    aws_job_id = models.CharField(
        verbose_name="AWS Job ID",
        max_length=200,
        blank=True,
        null=True
    )

    status = models.CharField(
        verbose_name="Status",
        max_length=50,
        choices=CONVERSION_STATUS_CHOICES,
        default="SUBMITTED"
    )

    # Output S3 Key (stored in output_url field for simplicity)
    output_url = models.Char<PERSON>ield(
        verbose_name="Output S3 Key",
        max_length=500,
        blank=True,
        null=True,
        help_text="S3 key for converted DASH manifest"
    )

    # Timestamps
    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)

    @property
    def output_s3_key(self):
        """Get the S3 key for the converted content"""
        return self.output_url

    @property
    def full_output_url(self):
        """Get the full output URL for the converted content (for API compatibility)"""
        if self.output_url:
            # Check if it's already a full URL (legacy data)
            if self.output_url.startswith('http'):
                return self.output_url
            # It's an S3 key, construct full URL
            from config import settings
            return f"{settings.AWS_MEDIA_CONVERTER_CDN_BASE_URL}/{self.output_url}"
        return None

    @property
    def is_complete(self) -> bool:
        """Check if conversion is complete"""
        return self.status == "COMPLETE"

    @property
    def is_in_progress(self) -> bool:
        """Check if conversion is still in progress"""
        return self.status == "SUBMITTED"

    def __str__(self):
        return f"MediaConversion {self.id} - {self.content.name} ({self.status})"

    class Meta:
        app_label = 'learn_content'
        verbose_name = "Media Conversion"
        verbose_name_plural = "Media Conversions"
        db_table = 'media_conversion'
        ordering = ['-created_date']
