# -*- coding: utf-8 -*-

import uuid
from django.db import models

from config import settings
from learn_content.models.learn_content import LearnContent

CONVERSION_STATUS_CHOICES = (
    ("SUBMITTED", "Submitted"),
    ("COMPLETE", "Complete"),
    ("ERROR", "Error"),
    ("CANC<PERSON>ED", "Canceled"),
)


class MediaConversion(models.Model):
    """
    Stores information about media conversion jobs for content
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Relationship to content
    content = models.OneToOneField(
        LearnContent,
        on_delete=models.CASCADE,
        related_name='media_conversion',
        verbose_name="Content"
    )
    
    # AWS Media Converter job information
    aws_job_id = models.CharField(
        verbose_name="AWS Job ID",
        max_length=200,
        blank=True,
        null=True
    )
    
    # Conversion status
    status = models.CharField(
        verbose_name="Status",
        max_length=50,
        choices=CONVERSION_STATUS_CHOICES,
        default="SUBMITTED"
    )
    
    # Output information
    output_url = models.URLField(
        verbose_name="Output URL",
        max_length=500,
        blank=True,
        null=True,
        help_text="Full URL for converted DASH manifest"
    )
    
    output_s3_key = models.CharField(
        verbose_name="Output S3 Key",
        max_length=500,
        blank=True,
        null=True,
        help_text="S3 key for converted DASH manifest"
    )
    
    # Timestamps
    created_date = models.DateTimeField(auto_now_add=True, verbose_name="Created Date")
    updated_date = models.DateTimeField(auto_now=True, verbose_name="Updated Date")

    class Meta:
        verbose_name = "Media Conversion"
        verbose_name_plural = "Media Conversions"
        db_table = "media_conversion"
        ordering = ["-created_date"]

    def __str__(self):
        return f"Conversion for {self.content.name} - {self.status}"

    @property
    def full_output_url(self):
        """Get the full output URL for the converted content (for API compatibility)"""
        # Priority: output_s3_key -> output_url
        if self.output_s3_key:
            return f"{settings.AWS_STREAMING_URL}/{self.output_s3_key}"
        elif self.output_url:
            return self.output_url
        return None

    def is_processing(self):
        """Check if conversion is still processing"""
        return self.status == "SUBMITTED"

    def is_complete(self):
        """Check if conversion is complete"""
        return self.status == "COMPLETE"

    def is_failed(self):
        """Check if conversion failed"""
        return self.status in ["ERROR", "CANCELED"]
