# -*- coding: utf-8 -*-
import uuid
from typing import <PERSON>ple

from django.db import models

from file_types import MEDIA_FILE, PDF, MEDIA_LINK, GOOGLE_DRIVE, MSOFFICE_FILE, IMAGE, SCORM
from learn_content.models.content_category import ContentCategory
from learn_content.models.content_type import ContentType


class LearnContent(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Name", max_length=200)
    description = models.TextField(verbose_name="Description", null=True, blank=True)
    category = models.ForeignKey(ContentCategory, verbose_name='Category', on_delete=models.PROTECT,
                                 null=True, blank=True)
    content_type = models.ForeignKey(ContentType, verbose_name='Type', on_delete=models.PROTECT, null=True, blank=True)
    url = models.URLField(verbose_name='URL', max_length=500, null=True, blank=True)
    s3_key = models.Char<PERSON><PERSON>(verbose_name='S3 Key', max_length=500, null=True, blank=True)
    created_date = models.DateTimeField(verbose_name="Created Date", auto_now_add=True)
    updated_date = models.DateTimeField(verbose_name="Updated Date", auto_now=True)

    is_whatsapp_content = models.BooleanField(default=False)
    is_valid_whatsapp_content = models.BooleanField(default=False)
    file_size = models.FloatField(verbose_name="File Size (MB)", null=True, blank=True)
    original_file_size = models.FloatField(verbose_name="Original File Size (MB)", null=True, blank=True)
    duration = models.IntegerField(verbose_name="File Duration", null=True, blank=True)
    analyzed = models.BooleanField(verbose_name="Analyzed", default=False)
    compressed = models.BooleanField(verbose_name="Compressed", default=False)
    transcribe_job = models.CharField(verbose_name="Transcribe Job", max_length=200, blank=True, null=True)
    file_mime_type = models.CharField(max_length=100, blank=True, null=True)

    @property
    def is_pdf(self) -> Tuple[bool, str]:
        return self.content_type.name == "PDF", PDF

    @property
    def is_image(self) -> Tuple[bool, str]:
        return self.content_type.name == "Image", IMAGE

    @property
    def is_media_file(self) -> Tuple[bool, str]:
        return self.transcribe_job is not None, MEDIA_FILE

    @property
    def is_media_link(self) -> Tuple[bool, str]:
        return self.content_type.name in ["Video", "Podcast"], MEDIA_LINK

    @property
    def is_drive_link(self) -> Tuple[bool, str]:
        return 'docs.google.com' in self.url, GOOGLE_DRIVE

    @property
    def is_ms_office_file(self) -> Tuple[bool, str]:
        return self.content_type.name in ["Spreadsheet", "Text", "Presentation"], MSOFFICE_FILE

    @property
    def is_scorm_file(self) -> Tuple[bool, str]:
        return self.content_type.name == "SCORM", SCORM

    @property
    def file_type(self) -> str:
        type_checks = (
            self.is_pdf,
            self.is_media_file,
            self.is_media_link,
            self.is_drive_link,
            self.is_ms_office_file,
            self.is_scorm_file,
            self.is_image
        )
        checks_passed = filter(lambda check: check[0], type_checks)
        if checks_passed:
            return next(checks_passed)[1]

    @property
    def full_url(self):
        """Get the full URL for this content (for API compatibility)"""
        # Priority: s3_key -> url
        if self.s3_key:
            return f"{settings.AWS_STREAMING_URL}/{self.s3_key}"
        elif self.url:
            return self.url
        return None

    class Meta:
        app_label = 'learn_content'
        verbose_name_plural = "Learn Content"
        db_table = 'learn_content'
