from rest_framework.exceptions import ValidationError

from config.settings import ELASTIC<PERSON>ARCH_INDEX
from elasticsearch_dsl import Date, Keyword, Text, Float, Object, Document, Boolean


class LearnContentDocument(Document):
    name = Text(analyzer='snowball', fields={'raw': Keyword()})
    description = Text(analyzer='snowball')
    category = Text(analyzer='snowball')
    url = Text(analyzer='snowball')
    s3_key = Text(analyzer='snowball')
    content_type = Object()

    content_transcript = Text(analyzer='snowball')
    duration = Float()
    points = Float()
    slides_count = Float()
    tags = Object(multi=True)
    summary = Text(analyzer='snowball')
    entities = Object(multi=True)
    language = Text()
    is_whatsapp_content = Boolean()
    is_valid_whatsapp_content = Boolean()
    file_size = Float()
    original_file_size = Float()
    compressed = Boolean()

    analyzed_error = Text()
    created_date = Date()
    updated_date = Date()

    def update_values(self, **kwargs):
        for name, values in kwargs.items():
            try:
                setattr(self, name, values)
            except KeyError as exception:
                raise ValidationError(f"Field {name} does not exist") from exception

    def save(self, **kwargs):
        return super(LearnContentDocument, self).save(**kwargs)

    class Index:
        name = ELASTICSEARCH_INDEX
