#!/usr/bin/env python3

# Script para testar MediaConverterService no sistema de DI
import os
import sys
import django

# Add the kontent directory to the path
sys.path.append('/home/<USER>/projects/keeps/keeps-kontent-server/kontent')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from di import Container
from learn_content.services.media_converter_service import MediaConverterService
from utils.aws.aws_media_converter import AwsMediaConverter

def test_container_media_converter_service():
    """Testa se o MediaConverterService está no Container"""
    print("🔧 Testando MediaConverterService no Container")
    print("=" * 60)
    
    try:
        container = Container()
        
        # Verificar se o método existe
        if hasattr(container, 'media_converter_service'):
            print(f"✅ Container.media_converter_service: MÉTODO EXISTE")
            
            # Testar criação do serviço
            service = container.media_converter_service()
            print(f"✅ Serviço criado: {type(service)}")
            
            if isinstance(service, MediaConverterService):
                print(f"✅ Tipo correto: MediaConverterService")
            else:
                print(f"❌ Tipo incorreto: {type(service)}")
                return False
                
            # Verificar se tem o media_converter client
            if hasattr(service, 'media_converter'):
                client = service.media_converter
                print(f"✅ service.media_converter: {type(client)}")
                
                if isinstance(client, AwsMediaConverter):
                    print(f"✅ Client correto: AwsMediaConverter")
                else:
                    print(f"❌ Client incorreto: {type(client)}")
                    return False
            else:
                print(f"❌ service.media_converter: NÃO ENCONTRADO")
                return False
                
        else:
            print(f"❌ Container.media_converter_service: MÉTODO NÃO ENCONTRADO")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao testar Container: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_singleton_behavior():
    """Testa se o serviço está sendo reutilizado (singleton)"""
    print(f"\n🔄 Testando comportamento Singleton")
    print("=" * 60)
    
    try:
        container1 = Container()
        container2 = Container()
        
        service1 = container1.media_converter_service()
        service2 = container2.media_converter_service()
        
        print(f"📋 Instância 1: {id(service1)}")
        print(f"📋 Instância 2: {id(service2)}")
        
        # Como o Container é singleton, mas o serviço é criado a cada chamada,
        # as instâncias podem ser diferentes, mas o AwsMediaConverter deve ser o mesmo
        client1 = service1.media_converter
        client2 = service2.media_converter
        
        print(f"📋 Client 1: {id(client1)}")
        print(f"📋 Client 2: {id(client2)}")
        
        if id(client1) == id(client2):
            print(f"✅ AwsMediaConverter reutilizado (singleton)")
        else:
            print(f"⚠️  AwsMediaConverter não é singleton")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste de singleton: {e}")
        return False

def test_service_methods():
    """Testa se os métodos do serviço estão funcionando"""
    print(f"\n🔧 Testando métodos do MediaConverterService")
    print("=" * 60)
    
    try:
        container = Container()
        service = container.media_converter_service()
        
        # Testar se os métodos existem
        methods_to_test = [
            'convert_video_if_needed',
            'update_conversion_status',
            '_handle_conversion_complete',
            '_build_output_s3_key'
        ]
        
        for method_name in methods_to_test:
            if hasattr(service, method_name):
                print(f"✅ Método {method_name}: EXISTE")
            else:
                print(f"❌ Método {method_name}: NÃO ENCONTRADO")
                return False
        
        # Testar método seguro
        client = service.media_converter
        should_convert = client.should_convert_file('video/mp4')
        print(f"✅ should_convert_file('video/mp4'): {should_convert}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao testar métodos: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_usage_patterns():
    """Compara os padrões de uso antigo vs novo"""
    print(f"\n🔄 Comparando padrões de uso")
    print("=" * 60)
    
    print("📋 Padrão ANTIGO (função helper):")
    print("   from learn_content.services.media_converter_service import create_media_converter_service")
    print("   service = create_media_converter_service()")
    
    print(f"\n📋 Padrão INTERMEDIÁRIO (DI manual):")
    print("   from di import Container")
    print("   from learn_content.services.media_converter_service import MediaConverterService")
    print("   service = MediaConverterService(Container().aws_media_converter_client())")
    
    print(f"\n📋 Padrão NOVO (DI completo):")
    print("   from di import Container")
    print("   service = Container().media_converter_service()")
    
    print(f"\n✅ Benefícios do padrão novo:")
    print("   ✅ Mais simples e direto")
    print("   ✅ Consistente com outros serviços")
    print("   ✅ Centralizado no Container")
    print("   ✅ Facilita testes e mocks")
    print("   ✅ Menos imports necessários")
    
    return True

def main():
    print("🚀 Teste de MediaConverterService no Sistema de DI")
    print("=" * 70)
    
    try:
        # Testar Container
        container_ok = test_container_media_converter_service()
        
        # Testar singleton
        singleton_ok = test_singleton_behavior()
        
        # Testar métodos
        methods_ok = test_service_methods()
        
        # Comparar padrões
        comparison_ok = compare_usage_patterns()
        
        print(f"\n📋 RESUMO FINAL:")
        print("=" * 70)
        
        if container_ok and singleton_ok and methods_ok and comparison_ok:
            print("🎉 MEDIACONVERTERSERVICE INTEGRADO COM SUCESSO AO DI!")
            print("✅ Container configurado corretamente")
            print("✅ Serviço criado via Container")
            print("✅ AwsMediaConverter reutilizado")
            print("✅ Todos os métodos funcionando")
            print("✅ Padrão de uso simplificado")
            print("✅ Código mais limpo e consistente")
        else:
            print("❌ PROBLEMAS ENCONTRADOS:")
            if not container_ok:
                print("   🔧 Problema na configuração do Container")
            if not singleton_ok:
                print("   🔧 Problema no comportamento singleton")
            if not methods_ok:
                print("   🔧 Problema nos métodos do serviço")
            if not comparison_ok:
                print("   🔧 Problema na comparação de padrões")
            
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
