#!/usr/bin/env python3

# Script para testar se o cascade delete está funcionando corretamente
import os
import sys
import django
import uuid

# Add the kontent directory to the path
sys.path.append('/home/<USER>/projects/keeps/keeps-kontent-server/kontent')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from learn_content.models import LearnContent, MediaConversion, ContentType

def test_cascade_delete():
    """Testa se o cascade delete está funcionando corretamente"""
    print("🔍 Testando Cascade Delete: LearnContent -> MediaConversion")
    print("=" * 60)
    
    # Criar um ContentType para teste (se não existir)
    content_type, created = ContentType.objects.get_or_create(
        name="Video",
        defaults={"description": "Video content for testing"}
    )
    
    if created:
        print(f"✅ ContentType 'Video' criado para teste")
    else:
        print(f"✅ ContentType 'Video' já existe")
    
    # Criar um LearnContent para teste
    test_content = LearnContent.objects.create(
        name="Test Video Content",
        description="Content created for cascade delete testing",
        content_type=content_type,
        url="https://example.com/test-video.mp4",
        s3_key="test/video.mp4",
        file_mime_type="video/mp4"
    )
    
    print(f"✅ LearnContent criado: {test_content.id}")
    
    # Criar um MediaConversion relacionado
    test_conversion = MediaConversion.objects.create(
        content=test_content,
        aws_job_id="test-job-123",
        status="SUBMITTED",
        output_s3_key="test/output/manifest.mpd"
    )
    
    print(f"✅ MediaConversion criado: {test_conversion.id}")
    
    # Verificar se a relação está funcionando
    print(f"\n🔗 Testando relação OneToOne:")
    
    # Testar acesso do LearnContent para MediaConversion
    conversion_from_content = test_content.get_media_conversion()
    if conversion_from_content:
        print(f"✅ LearnContent.get_media_conversion() -> {conversion_from_content.id}")
    else:
        print(f"❌ LearnContent.get_media_conversion() -> None")
        return False
    
    # Testar acesso do MediaConversion para LearnContent
    content_from_conversion = test_conversion.content
    print(f"✅ MediaConversion.content -> {content_from_conversion.id}")
    
    # Verificar se ambos os registros existem antes da deleção
    content_id = test_content.id
    conversion_id = test_conversion.id
    
    print(f"\n📊 Antes da deleção:")
    print(f"   LearnContent count: {LearnContent.objects.filter(id=content_id).count()}")
    print(f"   MediaConversion count: {MediaConversion.objects.filter(id=conversion_id).count()}")
    
    # TESTAR CASCADE DELETE
    print(f"\n🗑️  Deletando LearnContent...")
    test_content.delete()
    
    # Verificar se ambos foram deletados
    print(f"\n📊 Após a deleção:")
    content_exists = LearnContent.objects.filter(id=content_id).exists()
    conversion_exists = MediaConversion.objects.filter(id=conversion_id).exists()
    
    print(f"   LearnContent existe: {content_exists}")
    print(f"   MediaConversion existe: {conversion_exists}")
    
    # Verificar resultado
    if not content_exists and not conversion_exists:
        print(f"\n✅ SUCESSO: Cascade delete funcionando corretamente!")
        print(f"   ✅ LearnContent foi deletado")
        print(f"   ✅ MediaConversion foi deletado automaticamente")
        return True
    elif not content_exists and conversion_exists:
        print(f"\n❌ PROBLEMA: MediaConversion não foi deletado automaticamente!")
        print(f"   ✅ LearnContent foi deletado")
        print(f"   ❌ MediaConversion ainda existe (ORPHAN RECORD)")
        
        # Limpar o registro órfão
        MediaConversion.objects.filter(id=conversion_id).delete()
        print(f"   🧹 Registro órfão removido manualmente")
        return False
    else:
        print(f"\n❌ ERRO: Deleção não funcionou como esperado!")
        return False

def test_model_configuration():
    """Verifica a configuração do modelo"""
    print(f"\n🔧 Verificando configuração do modelo MediaConversion:")
    print("=" * 60)
    
    # Verificar o campo content
    content_field = MediaConversion._meta.get_field('content')
    
    print(f"✅ Tipo do campo: {type(content_field).__name__}")
    print(f"✅ on_delete: {content_field.remote_field.on_delete}")
    print(f"✅ related_name: {content_field.remote_field.related_name}")
    
    # Verificar se é CASCADE
    from django.db import models
    if content_field.remote_field.on_delete == models.CASCADE:
        print(f"✅ CASCADE DELETE configurado corretamente!")
        return True
    else:
        print(f"❌ CASCADE DELETE NÃO configurado!")
        return False

def main():
    print("🧪 Teste de Cascade Delete - LearnContent e MediaConversion")
    print("=" * 70)
    
    try:
        # Testar configuração do modelo
        config_ok = test_model_configuration()
        
        if config_ok:
            # Testar cascade delete na prática
            delete_ok = test_cascade_delete()
            
            print(f"\n📋 RESUMO FINAL:")
            print("=" * 70)
            
            if delete_ok:
                print("🎉 TUDO FUNCIONANDO PERFEITAMENTE!")
                print("✅ Configuração do modelo está correta")
                print("✅ Cascade delete está funcionando")
                print("✅ Quando um LearnContent é deletado, o MediaConversion é automaticamente removido")
            else:
                print("⚠️  PROBLEMA DETECTADO!")
                print("❌ Cascade delete não está funcionando corretamente")
                print("🔧 Verifique a configuração do modelo MediaConversion")
        else:
            print(f"\n❌ CONFIGURAÇÃO INCORRETA!")
            print("🔧 O campo 'content' no modelo MediaConversion não está configurado com CASCADE")
            
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
