#!/usr/bin/env python3

# Script para testar a construção correta do S3 key do MPD
import os
import sys
import django

# Add the kontent directory to the path
sys.path.append('/home/<USER>/projects/keeps/keeps-kontent-server/kontent')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def test_s3_key_construction_logic():
    """Testa a lógica de construção do S3 key"""
    print("🔧 Testando lógica de construção do S3 key")
    print("=" * 60)
    
    # Dados de exemplo baseados no seu caso
    test_cases = [
        {
            "name": "Caso Real",
            "file_input": "s3://keeps.kontent.media.hml/clients/e76b5082-f4fe-4f41-be79-1977840e16a8/general/teste_video_upload_e072.mp4",
            "destination": "s3://keeps.kontent.media.hml/clients/e76b5082-f4fe-4f41-be79-1977840e16a8/mpdash/cf69c319-3215-4ab9-9f41-c5c489339c6e/",
            "expected_s3_key": "clients/e76b5082-f4fe-4f41-be79-1977840e16a8/mpdash/cf69c319-3215-4ab9-9f41-c5c489339c6e/teste_video_upload_e072.mpd"
        },
        {
            "name": "Caso Simples",
            "file_input": "s3://bucket/path/video.mp4",
            "destination": "s3://bucket/output/",
            "expected_s3_key": "output/video.mpd"
        },
        {
            "name": "Caso com Múltiplos Pontos",
            "file_input": "s3://bucket/path/video.test.final.mp4",
            "destination": "s3://bucket/converted/",
            "expected_s3_key": "converted/video.test.final.mpd"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📋 {test_case['name']}:")
        
        # Simular a lógica do código
        file_input = test_case["file_input"]
        destination = test_case["destination"]
        expected = test_case["expected_s3_key"]
        
        # Extrair nome do arquivo (sem extensão)
        input_filename = file_input.split('/')[-1]
        input_name_without_ext = '.'.join(input_filename.split('.')[:-1])
        
        # Extrair S3 key do destination
        if destination.startswith('s3://'):
            destination_key = '/'.join(destination.split('/')[3:])
        else:
            destination_key = destination
        
        # Construir S3 key final
        constructed_s3_key = f"{destination_key}{input_name_without_ext}.mpd"
        
        print(f"   Input: {file_input}")
        print(f"   Destination: {destination}")
        print(f"   Filename: {input_filename}")
        print(f"   Name without ext: {input_name_without_ext}")
        print(f"   Destination key: {destination_key}")
        print(f"   Constructed: {constructed_s3_key}")
        print(f"   Expected: {expected}")
        
        if constructed_s3_key == expected:
            print(f"   ✅ CORRETO!")
        else:
            print(f"   ❌ INCORRETO!")
            return False
    
    return True

def simulate_aws_job_response():
    """Simula uma resposta real do AWS Media Converter"""
    print(f"\n📋 Simulando resposta do AWS Media Converter")
    print("=" * 60)
    
    mock_job_response = {
        "Job": {
            "Id": "1234567890-abcdef",
            "Status": "COMPLETE",
            "JobPercentComplete": 100,
            "CreatedAt": "2025-01-11T10:00:00Z",
            "FinishedAt": "2025-01-11T10:05:00Z",
            "Settings": {
                "Inputs": [
                    {
                        "FileInput": "s3://keeps.kontent.media.hml/clients/e76b5082-f4fe-4f41-be79-1977840e16a8/general/teste_video_upload_e072.mp4"
                    }
                ],
                "OutputGroups": [
                    {
                        "OutputGroupSettings": {
                            "Type": "DASH_ISO_GROUP_SETTINGS",
                            "DashIsoGroupSettings": {
                                "Destination": "s3://keeps.kontent.media.hml/clients/e76b5082-f4fe-4f41-be79-1977840e16a8/mpdash/cf69c319-3215-4ab9-9f41-c5c489339c6e/"
                            }
                        }
                    }
                ]
            }
        }
    }
    
    print("📋 Estrutura da resposta AWS:")
    print("   Job.Settings.Inputs[0].FileInput -> arquivo de entrada")
    print("   Job.Settings.OutputGroups[].OutputGroupSettings.DashIsoGroupSettings.Destination -> pasta de saída")
    
    # Simular a extração
    job = mock_job_response["Job"]
    
    try:
        # Get the input file name (without extension)
        input_file_uri = job['Settings']['Inputs'][0]['FileInput']
        input_filename = input_file_uri.split('/')[-1]
        input_name_without_ext = '.'.join(input_filename.split('.')[:-1])
        
        # Get the destination path
        dash_output_group = None
        for output_group in job['Settings']['OutputGroups']:
            if output_group.get('OutputGroupSettings', {}).get('Type') == 'DASH_ISO_GROUP_SETTINGS':
                dash_output_group = output_group
                break
        
        if dash_output_group:
            destination = dash_output_group['OutputGroupSettings']['DashIsoGroupSettings']['Destination']
            # Extract S3 key from destination
            if destination.startswith('s3://'):
                destination_key = '/'.join(destination.split('/')[3:])
                output_s3_key = f"{destination_key}{input_name_without_ext}.mpd"
        
        print(f"\n✅ Extração bem-sucedida:")
        print(f"   Input file: {input_file_uri}")
        print(f"   Filename: {input_filename}")
        print(f"   Name without ext: {input_name_without_ext}")
        print(f"   Destination: {destination}")
        print(f"   Destination key: {destination_key}")
        print(f"   Final S3 key: {output_s3_key}")
        
        expected = "clients/e76b5082-f4fe-4f41-be79-1977840e16a8/mpdash/cf69c319-3215-4ab9-9f41-c5c489339c6e/teste_video_upload_e072.mpd"
        
        if output_s3_key == expected:
            print(f"   ✅ Resultado correto!")
            return True
        else:
            print(f"   ❌ Resultado incorreto!")
            print(f"   Expected: {expected}")
            return False
        
    except Exception as e:
        print(f"❌ Erro na extração: {e}")
        return False

def test_edge_cases():
    """Testa casos extremos"""
    print(f"\n🔍 Testando casos extremos")
    print("=" * 60)
    
    edge_cases = [
        {
            "name": "Arquivo sem extensão",
            "filename": "video",
            "expected_name": "video"
        },
        {
            "name": "Arquivo com múltiplas extensões",
            "filename": "video.backup.final.mp4",
            "expected_name": "video.backup.final"
        },
        {
            "name": "Arquivo com ponto no nome",
            "filename": "my.video.mp4",
            "expected_name": "my.video"
        }
    ]
    
    for case in edge_cases:
        filename = case["filename"]
        expected = case["expected_name"]
        
        # Lógica de extração
        name_without_ext = '.'.join(filename.split('.')[:-1]) if '.' in filename else filename
        
        print(f"   {case['name']}: '{filename}' -> '{name_without_ext}'")
        
        if name_without_ext == expected:
            print(f"     ✅ Correto")
        else:
            print(f"     ❌ Incorreto (esperado: '{expected}')")
            return False
    
    return True

def main():
    print("🚀 Teste de Construção do S3 Key do MPD")
    print("=" * 70)
    
    try:
        # Testar lógica de construção
        logic_ok = test_s3_key_construction_logic()
        
        # Simular resposta AWS
        aws_ok = simulate_aws_job_response()
        
        # Testar casos extremos
        edge_ok = test_edge_cases()
        
        print(f"\n📋 RESUMO FINAL:")
        print("=" * 70)
        
        if logic_ok and aws_ok and edge_ok:
            print("🎉 LÓGICA DE CONSTRUÇÃO DO S3 KEY ESTÁ CORRETA!")
            print("✅ Casos de teste passaram")
            print("✅ Simulação AWS funcionou")
            print("✅ Casos extremos cobertos")
            print("✅ Formato: destination_key + filename_without_ext + .mpd")
            print("✅ Exemplo: clients/workspace/mpdash/content_id/filename.mpd")
        else:
            print("❌ PROBLEMAS ENCONTRADOS:")
            if not logic_ok:
                print("   🔧 Problema na lógica de construção")
            if not aws_ok:
                print("   🔧 Problema na simulação AWS")
            if not edge_ok:
                print("   🔧 Problema nos casos extremos")
            
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
